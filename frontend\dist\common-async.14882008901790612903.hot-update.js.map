{"version": 3, "sources": ["common-async.14882008901790612903.hot-update.js", "src/pages/team/components/TeamListContent.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'common',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='3207233597650937199';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/create/index.tsx\":[\"p__team__create__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "/**\n * 团队列表内容组件\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  List, \n  Avatar, \n  Button, \n  Space, \n  Typography, \n  Tag, \n  Input,\n  message,\n  Empty\n} from 'antd';\nimport { \n  TeamOutlined, \n  UserOutlined, \n  SearchOutlined,\n  CrownOutlined,\n  EyeOutlined\n} from '@ant-design/icons';\nimport { history } from '@umijs/max';\nimport { TeamService, AuthService } from '@/services';\nimport type { TeamDetailResponse } from '@/types/api';\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\n\nconst TeamListContent: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);\n  const [filteredTeams, setFilteredTeams] = useState<TeamDetailResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n\n  useEffect(() => {\n    fetchTeams();\n  }, []);\n\n  useEffect(() => {\n    // 过滤团队列表\n    const filtered = teams.filter(team =>\n      team.name.toLowerCase().includes(searchText.toLowerCase()) ||\n      (team.description && team.description.toLowerCase().includes(searchText.toLowerCase()))\n    );\n    setFilteredTeams(filtered);\n  }, [teams, searchText]);\n\n  const fetchTeams = async () => {\n    try {\n      setLoading(true);\n      const teamList = await TeamService.getUserTeams();\n      setTeams(teamList);\n    } catch (error) {\n      console.error('获取团队列表失败:', error);\n      message.error('获取团队列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateTeam = () => {\n    history.push('/team/create');\n  };\n\n  const handleViewTeam = async (team: TeamDetailResponse) => {\n    try {\n      // 切换到该团队\n      const response = await AuthService.teamLogin({ teamId: team.id });\n\n      // 检查后端返回的团队选择成功标识\n      if (response.teamSelectionSuccess && response.team && response.team.id === team.id) {\n        message.success(`已切换到团队：${team.name}`);\n        // 不需要刷新页面，让应用自然响应状态变化\n      } else {\n        console.error('团队切换响应异常，未返回正确的团队信息');\n        message.error('团队切换失败，请重试');\n      }\n    } catch (error) {\n      console.error('切换团队失败:', error);\n      message.error('切换团队失败');\n    }\n  };\n\n  const handleSwitchTeam = async (team: TeamDetailResponse) => {\n    try {\n      const response = await AuthService.teamLogin({ teamId: team.id });\n\n      // 检查后端返回的团队选择成功标识\n      if (response.teamSelectionSuccess && response.team && response.team.id === team.id) {\n        message.success(`已切换到团队：${team.name}`);\n\n        // 等待一段时间确保 Token 更新完成后再跳转\n        setTimeout(() => {\n          history.push('/');\n        }, 200);\n      } else {\n        console.error('团队切换响应异常，未返回正确的团队信息');\n        message.error('团队切换失败，请重试');\n      }\n    } catch (error) {\n      console.error('切换团队失败:', error);\n      message.error('切换团队失败');\n    }\n  };\n\n  return (\n    <div>\n      <div style={{ marginBottom: 16 }}>\n        <Search\n          placeholder=\"搜索团队名称或描述\"\n          allowClear\n          value={searchText}\n          onChange={(e) => setSearchText(e.target.value)}\n          style={{ width: 300 }}\n        />\n      </div>\n\n      {filteredTeams.length === 0 && !loading ? (\n        <Empty\n          image={Empty.PRESENTED_IMAGE_SIMPLE}\n          description={\n            searchText ? '没有找到匹配的团队' : '您还没有加入任何团队'\n          }\n        >\n          {!searchText && (\n            <Button type=\"primary\" onClick={handleCreateTeam}>\n              创建第一个团队\n            </Button>\n          )}\n        </Empty>\n      ) : (\n        <List\n          loading={loading}\n          itemLayout=\"horizontal\"\n          dataSource={filteredTeams}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个团队`,\n          }}\n          renderItem={(team) => (\n            <List.Item\n              actions={[\n                <Button\n                  key=\"view\"\n                  type=\"text\"\n                  icon={<EyeOutlined />}\n                  onClick={() => handleViewTeam(team)}\n                >\n                  查看详情\n                </Button>,\n                <Button\n                  key=\"switch\"\n                  type=\"primary\"\n                  size=\"small\"\n                  onClick={() => handleSwitchTeam(team)}\n                >\n                  进入团队\n                </Button>,\n              ]}\n            >\n              <List.Item.Meta\n                avatar={\n                  <Avatar \n                    size={64} \n                    icon={<TeamOutlined />}\n                    style={{ backgroundColor: '#1890ff' }}\n                  />\n                }\n                title={\n                  <Space>\n                    <Title level={4} style={{ margin: 0 }}>\n                      {team.name}\n                    </Title>\n                    {team.isCreator && (\n                      <Tag color=\"gold\" icon={<CrownOutlined />}>\n                        创建者\n                      </Tag>\n                    )}\n                  </Space>\n                }\n                description={\n                  <Space direction=\"vertical\" size=\"small\">\n                    {team.description && (\n                      <Text type=\"secondary\">{team.description}</Text>\n                    )}\n                    <Space>\n                      <Space size=\"small\">\n                        <UserOutlined />\n                        <Text type=\"secondary\">{team.memberCount} 名成员</Text>\n                      </Space>\n                      <Text type=\"secondary\">\n                        创建于 {new Date(team.createdAt).toLocaleDateString()}\n                      </Text>\n                      <Text type=\"secondary\">\n                        更新于 {new Date(team.updatedAt).toLocaleDateString()}\n                      </Text>\n                    </Space>\n                  </Space>\n                }\n              />\n            </List.Item>\n          )}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default TeamListContent;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,UACA;IACE,SAAS;;;;;;wCCgNb;;;2BAAA;;;;;;oFA/M2C;yCAWpC;0CAOA;wCACiB;6CACiB;;;;;;;;;;YAGzC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAClC,MAAM,EAAE,MAAM,EAAE,GAAG,WAAK;YAExB,MAAM,kBAA4B;;gBAChC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAuB,EAAE;gBAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAuB,EAAE;gBAC3E,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;gBAE7C,IAAA,gBAAS,EAAC;oBACR;gBACF,GAAG,EAAE;gBAEL,IAAA,gBAAS,EAAC;oBACR,SAAS;oBACT,MAAM,WAAW,MAAM,MAAM,CAAC,CAAA,OAC5B,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACtD,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;oBAErF,iBAAiB;gBACnB,GAAG;oBAAC;oBAAO;iBAAW;gBAEtB,MAAM,aAAa;oBACjB,IAAI;wBACF,WAAW;wBACX,MAAM,WAAW,MAAM,qBAAW,CAAC,YAAY;wBAC/C,SAAS;oBACX,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;oBAChB,SAAU;wBACR,WAAW;oBACb;gBACF;gBAEA,MAAM,mBAAmB;oBACvB,YAAO,CAAC,IAAI,CAAC;gBACf;gBAEA,MAAM,iBAAiB,OAAO;oBAC5B,IAAI;wBACF,SAAS;wBACT,MAAM,WAAW,MAAM,qBAAW,CAAC,SAAS,CAAC;4BAAE,QAAQ,KAAK,EAAE;wBAAC;wBAE/D,kBAAkB;wBAClB,IAAI,SAAS,oBAAoB,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,EAAE,KAAK,KAAK,EAAE,EAChF,aAAO,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC;6BAEhC;4BACL,QAAQ,KAAK,CAAC;4BACd,aAAO,CAAC,KAAK,CAAC;wBAChB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAEA,MAAM,mBAAmB,OAAO;oBAC9B,IAAI;wBACF,MAAM,WAAW,MAAM,qBAAW,CAAC,SAAS,CAAC;4BAAE,QAAQ,KAAK,EAAE;wBAAC;wBAE/D,kBAAkB;wBAClB,IAAI,SAAS,oBAAoB,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,EAAE,KAAK,KAAK,EAAE,EAAE;4BAClF,aAAO,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC;4BAErC,0BAA0B;4BAC1B,WAAW;gCACT,YAAO,CAAC,IAAI,CAAC;4BACf,GAAG;wBACL,OAAO;4BACL,QAAQ,KAAK,CAAC;4BACd,aAAO,CAAC,KAAK,CAAC;wBAChB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAEA,qBACE,2BAAC;;sCACC,2BAAC;4BAAI,OAAO;gCAAE,cAAc;4BAAG;sCAC7B,cAAA,2BAAC;gCACC,aAAY;gCACZ,UAAU;gCACV,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,OAAO;oCAAE,OAAO;gCAAI;;;;;;;;;;;wBAIvB,cAAc,MAAM,KAAK,KAAK,CAAC,wBAC9B,2BAAC,WAAK;4BACJ,OAAO,WAAK,CAAC,sBAAsB;4BACnC,aACE,aAAa,cAAc;sCAG5B,CAAC,4BACA,2BAAC,YAAM;gCAAC,MAAK;gCAAU,SAAS;0CAAkB;;;;;;;;;;iDAMtD,2BAAC,UAAI;4BACH,SAAS;4BACT,YAAW;4BACX,YAAY;4BACZ,YAAY;gCACV,iBAAiB;gCACjB,iBAAiB;gCACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;4BACxC;4BACA,YAAY,CAAC,qBACX,2BAAC,UAAI,CAAC,IAAI;oCACR,SAAS;sDACP,2BAAC,YAAM;4CAEL,MAAK;4CACL,oBAAM,2BAAC,kBAAW;;;;;4CAClB,SAAS,IAAM,eAAe;sDAC/B;2CAJK;;;;;sDAON,2BAAC,YAAM;4CAEL,MAAK;4CACL,MAAK;4CACL,SAAS,IAAM,iBAAiB;sDACjC;2CAJK;;;;;qCAOP;8CAED,cAAA,2BAAC,UAAI,CAAC,IAAI,CAAC,IAAI;wCACb,sBACE,2BAAC,YAAM;4CACL,MAAM;4CACN,oBAAM,2BAAC,mBAAY;;;;;4CACnB,OAAO;gDAAE,iBAAiB;4CAAU;;;;;;wCAGxC,qBACE,2BAAC,WAAK;;8DACJ,2BAAC;oDAAM,OAAO;oDAAG,OAAO;wDAAE,QAAQ;oDAAE;8DACjC,KAAK,IAAI;;;;;;gDAEX,KAAK,SAAS,kBACb,2BAAC,SAAG;oDAAC,OAAM;oDAAO,oBAAM,2BAAC,oBAAa;;;;;8DAAK;;;;;;;;;;;;wCAMjD,2BACE,2BAAC,WAAK;4CAAC,WAAU;4CAAW,MAAK;;gDAC9B,KAAK,WAAW,kBACf,2BAAC;oDAAK,MAAK;8DAAa,KAAK,WAAW;;;;;;8DAE1C,2BAAC,WAAK;;sEACJ,2BAAC,WAAK;4DAAC,MAAK;;8EACV,2BAAC,mBAAY;;;;;8EACb,2BAAC;oEAAK,MAAK;;wEAAa,KAAK,WAAW;wEAAC;;;;;;;;;;;;;sEAE3C,2BAAC;4DAAK,MAAK;;gEAAY;gEAChB,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;sEAElD,2BAAC;4DAAK,MAAK;;gEAAY;gEAChB,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAYxE;eAnLM;iBAAA;gBAqLN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDhND;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;QAAC,wCAAuC;YAAC;SAAuC;IAAA;;AAC9yB"}