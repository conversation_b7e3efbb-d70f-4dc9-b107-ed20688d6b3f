{"version": 3, "sources": ["umi.11496620640622007475.hot-update.js", "config/defaultSettings.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='8001390792698991474';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/create/index.tsx\":[\"p__team__create__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "import type { ProLayoutProps } from '@ant-design/pro-components';\r\n\r\n/**\r\n * @name\r\n */\r\nconst Settings: ProLayoutProps & {\r\n  pwa?: boolean;\r\n  logo?: string;\r\n} = {\r\n  navTheme: 'light',\r\n  // 拂晓蓝\r\n  colorPrimary: '#1890ff',\r\n  layout: 'side',\r\n  contentWidth: 'Fluid',\r\n  fixedHeader: false,\r\n  fixSiderbar: true,\r\n  colorWeak: false,\r\n  title: '团队协作管理系统',\r\n  pwa: false,\r\n  logo: '/logo.svg',\r\n  iconfontUrl: '',\r\n  token: {\r\n    // 参见ts声明，demo 见文档，通过token 修改样式\r\n    //https://procomponents.ant.design/components/layout#%E9%80%9A%E8%BF%87-token-%E4%BF%AE%E6%94%B9%E6%A0%B7%E5%BC%8F\r\n  },\r\n};\r\n\r\nexport default Settings;\r\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;wCCwBb;;;2BAAA;;;;;;;;;;;;;YAzBA;;CAEC,GACD,MAAM,WAGF;gBACF,UAAU;gBACV,MAAM;gBACN,cAAc;gBACd,QAAQ;gBACR,cAAc;gBACd,aAAa;gBACb,aAAa;gBACb,WAAW;gBACX,OAAO;gBACP,KAAK;gBACL,MAAM;gBACN,aAAa;gBACb,OAAO;gBAGP;YACF;gBAEA,WAAe;;;;;;;;;;;;;;;;;;;;;;;IDxBD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;QAAC,wCAAuC;YAAC;SAAuC;IAAA;;AAC9yB"}