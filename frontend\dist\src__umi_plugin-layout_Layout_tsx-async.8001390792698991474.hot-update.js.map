{"version": 3, "sources": ["src__umi_plugin-layout_Layout_tsx-async.8001390792698991474.hot-update.js", "src/.umi/plugin-layout/Layout.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/plugin-layout/Layout.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='13836615729618659876';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/create/index.tsx\":[\"p__team__create__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "// @ts-nocheck\n// This file is generated by <PERSON><PERSON> automatically\n// DO NOT CHANGE IT MANUALLY!\n/// <reference types=\"@ant-design/pro-components\" />\n/// <reference types=\"antd\" />\n\nimport {\n  Link, useLocation, useNavigate, Outlet, useAppData, matchRoutes,\n  type IRoute\n} from '@umijs/max';\nimport React, { useMemo } from 'react';\nimport {\n  ProLayout,\n} from \"H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@ant-design/pro-components\";\nimport './Layout.css';\nimport Logo from './Logo';\nimport Exception from './Exception';\nimport { getRightRenderContent } from './rightRender';\nimport { useModel } from '@@/plugin-model';\nimport { useAccessMarkedRoutes } from '@@/plugin-access';\n\n\n// 过滤出需要显示的路由, 这里的filterFn 指 不希望显示的层级\nconst filterRoutes = (routes: IRoute[], filterFn: (route: IRoute) => boolean) => {\n  if (routes.length === 0) {\n    return []\n  }\n\n  let newRoutes = []\n  for (const route of routes) {\n    const newRoute = {...route };\n    if (filterFn(route)) {\n      if (Array.isArray(newRoute.routes)) {\n        newRoutes.push(...filterRoutes(newRoute.routes, filterFn))\n      }\n    } else {\n      if (Array.isArray(newRoute.children)) {\n        newRoute.children = filterRoutes(newRoute.children, filterFn);\n        newRoute.routes = newRoute.children;\n      }\n      newRoutes.push(newRoute);\n    }\n  }\n\n  return newRoutes;\n}\n\n// 格式化路由 处理因 wrapper 导致的 菜单 path 不一致\nconst mapRoutes = (routes: IRoute[]) => {\n  if (routes.length === 0) {\n    return []\n  }\n  return routes.map(route => {\n    // 需要 copy 一份, 否则会污染原始数据\n    const newRoute = {...route}\n    if (route.originPath) {\n      newRoute.path = route.originPath\n    }\n\n    if (Array.isArray(route.routes)) {\n      newRoute.routes = mapRoutes(route.routes);\n    }\n\n    if (Array.isArray(route.children)) {\n      newRoute.children = mapRoutes(route.children);\n    }\n\n    return newRoute\n  })\n}\n\nexport default (props: any) => {\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { clientRoutes, pluginManager } = useAppData();\n  const initialInfo = (useModel && useModel('@@initialState')) || {\n    initialState: undefined,\n    loading: false,\n    setInitialState: null,\n  };\n  const { initialState, loading, setInitialState } = initialInfo;\n  const userConfig = {\n  \"locale\": false,\n  \"navTheme\": \"light\",\n  \"colorPrimary\": \"#1890ff\",\n  \"layout\": \"side\",\n  \"contentWidth\": \"Fluid\",\n  \"fixedHeader\": false,\n  \"fixSiderbar\": true,\n  \"colorWeak\": false,\n  \"title\": \"团队协作管理系统\",\n  \"pwa\": false,\n  \"logo\": \"/logo.svg\",\n  \"iconfontUrl\": \"\",\n  \"token\": {}\n};\nconst formatMessage = undefined;\n  const runtimeConfig = pluginManager.applyPlugins({\n    key: 'layout',\n    type: 'modify',\n    initialValue: {\n      ...initialInfo\n    },\n  });\n\n\n  // 现在的 layout 及 wrapper 实现是通过父路由的形式实现的, 会导致路由数据多了冗余层级, proLayout 消费时, 无法正确展示菜单, 这里对冗余数据进行过滤操作\n  const newRoutes = filterRoutes(clientRoutes.filter(route => route.id === 'ant-design-pro-layout'), (route) => {\n    return (!!route.isLayout && route.id !== 'ant-design-pro-layout') || !!route.isWrapper;\n  })\n  const [route] = useAccessMarkedRoutes(mapRoutes(newRoutes));\n\n  const matchedRoute = useMemo(() => matchRoutes(route.children, location.pathname)?.pop?.()?.route, [location.pathname]);\n\n  return (\n    <ProLayout\n      route={route}\n      location={location}\n      title={userConfig.title || 'ant-design-pro'}\n      navTheme=\"dark\"\n      siderWidth={256}\n      onMenuHeaderClick={(e) => {\n        e.stopPropagation();\n        e.preventDefault();\n        navigate('/');\n      }}\n      formatMessage={userConfig.formatMessage || formatMessage}\n      menu={{ locale: userConfig.locale }}\n      logo={Logo}\n      menuItemRender={(menuItemProps, defaultDom) => {\n        if (menuItemProps.isUrl || menuItemProps.children) {\n          return defaultDom;\n        }\n        if (menuItemProps.path && location.pathname !== menuItemProps.path) {\n          return (\n            // handle wildcard route path, for example /slave/* from qiankun\n            <Link to={menuItemProps.path.replace('/*', '')} target={menuItemProps.target}>\n              {defaultDom}\n            </Link>\n          );\n        }\n        return defaultDom;\n      }}\n      itemRender={(route, _, routes) => {\n        const { breadcrumbName, title, path } = route;\n        const label = title || breadcrumbName\n        const last = routes[routes.length - 1]\n        if (last) {\n          if (last.path === path || last.linkPath === path) {\n            return <span>{label}</span>;\n          }\n        }\n        return <Link to={path}>{label}</Link>;\n      }}\n      disableContentMargin\n      fixSiderbar\n      fixedHeader\n      {...runtimeConfig}\n      rightContentRender={\n        runtimeConfig.rightContentRender !== false &&\n        ((layoutProps) => {\n          const dom = getRightRenderContent({\n            runtimeConfig,\n            loading,\n            initialState,\n            setInitialState,\n          });\n          if (runtimeConfig.rightContentRender) {\n            return runtimeConfig.rightContentRender(layoutProps, dom, {\n              // BREAK CHANGE userConfig > runtimeConfig\n              userConfig,\n              runtimeConfig,\n              loading,\n              initialState,\n              setInitialState,\n            });\n          }\n          return dom;\n        })\n      }\n    >\n      <Exception\n        route={matchedRoute}\n        noFound={runtimeConfig?.noFound}\n        notFound={runtimeConfig?.notFound}\n        unAccessible={runtimeConfig?.unAccessible}\n        noAccessible={runtimeConfig?.noAccessible}\n      >\n        {runtimeConfig.childrenRender\n          ? runtimeConfig.childrenRender(<Outlet />, props)\n          : <Outlet />\n        }\n      </Exception>\n    </ProLayout>\n  );\n}\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,qCACA;IACE,SAAS;;;;;;;;;;;;;;;;wCCMN;oFACwB;kDAGxB;;kFAEU;uFACK;gDACgB;gDACb;iDACa;;;;;;;;;;YAGtC,qCAAqC;YACrC,MAAM,eAAe,CAAC,QAAkB;gBACtC,IAAI,OAAO,MAAM,KAAK,GACpB,OAAO,EAAE;gBAGX,IAAI,YAAY,EAAE;gBAClB,KAAK,MAAM,SAAS,OAAQ;oBAC1B,MAAM,WAAW;wBAAC,GAAG,KAAK;oBAAC;oBAC3B,IAAI,SAAS,QACX;wBAAA,IAAI,MAAM,OAAO,CAAC,SAAS,MAAM,GAC/B,UAAU,IAAI,IAAI,aAAa,SAAS,MAAM,EAAE;oBAClD,OACK;wBACL,IAAI,MAAM,OAAO,CAAC,SAAS,QAAQ,GAAG;4BACpC,SAAS,QAAQ,GAAG,aAAa,SAAS,QAAQ,EAAE;4BACpD,SAAS,MAAM,GAAG,SAAS,QAAQ;wBACrC;wBACA,UAAU,IAAI,CAAC;oBACjB;gBACF;gBAEA,OAAO;YACT;YAEA,oCAAoC;YACpC,MAAM,YAAY,CAAC;gBACjB,IAAI,OAAO,MAAM,KAAK,GACpB,OAAO,EAAE;gBAEX,OAAO,OAAO,GAAG,CAAC,CAAA;oBAChB,wBAAwB;oBACxB,MAAM,WAAW;wBAAC,GAAG,KAAK;oBAAA;oBAC1B,IAAI,MAAM,UAAU,EAClB,SAAS,IAAI,GAAG,MAAM,UAAU;oBAGlC,IAAI,MAAM,OAAO,CAAC,MAAM,MAAM,GAC5B,SAAS,MAAM,GAAG,UAAU,MAAM,MAAM;oBAG1C,IAAI,MAAM,OAAO,CAAC,MAAM,QAAQ,GAC9B,SAAS,QAAQ,GAAG,UAAU,MAAM,QAAQ;oBAG9C,OAAO;gBACT;YACF;YAEe,qBAAC;;gBACd,MAAM,WAAW,IAAA,gBAAW;gBAC5B,MAAM,WAAW,IAAA,gBAAW;gBAC5B,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG,IAAA,eAAU;gBAClD,MAAM,cAAc,AAAC,qBAAQ,IAAI,IAAA,qBAAQ,EAAC,qBAAsB;oBAC9D,cAAc;oBACd,SAAS;oBACT,iBAAiB;gBACnB;gBACA,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG;gBACnD,MAAM,aAAa;oBACnB,UAAU;oBACV,YAAY;oBACZ,gBAAgB;oBAChB,UAAU;oBACV,gBAAgB;oBAChB,eAAe;oBACf,eAAe;oBACf,aAAa;oBACb,SAAS;oBACT,OAAO;oBACP,QAAQ;oBACR,eAAe;oBACf,SAAS,CAAC;gBACZ;gBACA,MAAM,gBAAgB;gBACpB,MAAM,gBAAgB,cAAc,YAAY,CAAC;oBAC/C,KAAK;oBACL,MAAM;oBACN,cAAc;wBACZ,GAAG,WAAW;oBAChB;gBACF;gBAGA,6FAA6F;gBAC7F,MAAM,YAAY,aAAa,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,0BAA0B,CAAC;oBAClG,OAAO,AAAC,CAAC,CAAC,MAAM,QAAQ,IAAI,MAAM,EAAE,KAAK,2BAA4B,CAAC,CAAC,MAAM,SAAS;gBACxF;gBACA,MAAM,CAAC,MAAM,GAAG,IAAA,mCAAqB,EAAC,UAAU;gBAEhD,MAAM,eAAe,IAAA,cAAO,EAAC;wBAAM,kBAAA,mBAAA;4BAAA,eAAA,IAAA,gBAAW,EAAC,MAAM,QAAQ,EAAE,SAAS,QAAQ,eAA7C,oCAAA,oBAAA,aAAgD,GAAG,cAAnD,yCAAA,mBAAA,uBAAA,2BAAA,uCAAA,iBAAyD,KAAK;mBAAE;oBAAC,SAAS,QAAQ;iBAAC;gBAEtH,qBACE,2BAAC,wBAAS;oBACR,OAAO;oBACP,UAAU;oBACV,OAAO,WAAW,KAAK,IAAI;oBAC3B,UAAS;oBACT,YAAY;oBACZ,mBAAmB,CAAC;wBAClB,EAAE,eAAe;wBACjB,EAAE,cAAc;wBAChB,SAAS;oBACX;oBACA,eAAe,WAAW,aAAa,IAAI;oBAC3C,MAAM;wBAAE,QAAQ,WAAW,MAAM;oBAAC;oBAClC,MAAM,aAAI;oBACV,gBAAgB,CAAC,eAAe;wBAC9B,IAAI,cAAc,KAAK,IAAI,cAAc,QAAQ,EAC/C,OAAO;wBAET,IAAI,cAAc,IAAI,IAAI,SAAS,QAAQ,KAAK,cAAc,IAAI,EAChE,OACE,gEAAgE;sCAChE,2BAAC,SAAI;4BAAC,IAAI,cAAc,IAAI,CAAC,OAAO,CAAC,MAAM;4BAAK,QAAQ,cAAc,MAAM;sCACzE;;;;;;wBAIP,OAAO;oBACT;oBACA,YAAY,CAAC,OAAO,GAAG;wBACrB,MAAM,EAAE,cAAc,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG;wBACxC,MAAM,QAAQ,SAAS;wBACvB,MAAM,OAAO,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;wBACtC,IAAI,MAAM;4BACR,IAAI,KAAK,IAAI,KAAK,QAAQ,KAAK,QAAQ,KAAK,MAC1C,qBAAO,2BAAC;0CAAM;;;;;;wBAElB;wBACA,qBAAO,2BAAC,SAAI;4BAAC,IAAI;sCAAO;;;;;;oBAC1B;oBACA,oBAAoB;oBACpB,WAAW;oBACX,WAAW;oBACV,GAAG,aAAa;oBACjB,oBACE,cAAc,kBAAkB,KAAK,SACpC,CAAA,CAAC;wBACA,MAAM,MAAM,IAAA,kCAAqB,EAAC;4BAChC;4BACA;4BACA;4BACA;wBACF;wBACA,IAAI,cAAc,kBAAkB,EAClC,OAAO,cAAc,kBAAkB,CAAC,aAAa,KAAK;4BACxD,0CAA0C;4BAC1C;4BACA;4BACA;4BACA;4BACA;wBACF;wBAEF,OAAO;oBACT,CAAA;8BAGF,cAAA,2BAAC,kBAAS;wBACR,OAAO;wBACP,OAAO,EAAE,0BAAA,oCAAA,cAAe,OAAO;wBAC/B,QAAQ,EAAE,0BAAA,oCAAA,cAAe,QAAQ;wBACjC,YAAY,EAAE,0BAAA,oCAAA,cAAe,YAAY;wBACzC,YAAY,EAAE,0BAAA,oCAAA,cAAe,YAAY;kCAExC,cAAc,cAAc,GACzB,cAAc,cAAc,eAAC,2BAAC,WAAM;;;;kCAAK,uBACzC,2BAAC,WAAM;;;;;;;;;;;;;;;YAKnB;;;oBA3HmB,gBAAW;oBACX,gBAAW;oBACY,eAAU;oBACjB,qBAAQ;oBAmCzB,mCAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;ID3GzB;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;QAAC,wCAAuC;YAAC;SAAuC;IAAA;;AAC9yB"}