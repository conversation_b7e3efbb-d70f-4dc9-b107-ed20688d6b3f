((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['common'],
{ "src/pages/subscription/components/SubscriptionManageContent.tsx": function (module, exports, __mako_require__){
/**
 * 订阅管理内容组件
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var _api = __mako_require__("src/types/api.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
const SubscriptionManageContent = ({ currentSubscription, loading, onRefresh })=>{
    _s();
    const [subscriptionHistory, setSubscriptionHistory] = (0, _react.useState)([]);
    const [usageInfo, setUsageInfo] = (0, _react.useState)(null);
    const [historyModalVisible, setHistoryModalVisible] = (0, _react.useState)(false);
    (0, _react.useEffect)(()=>{
        if (currentSubscription) {
            fetchSubscriptionHistory();
            fetchUsageInfo();
        }
    }, [
        currentSubscription
    ]);
    const fetchSubscriptionHistory = async ()=>{
        try {
            const history = await _services.SubscriptionService.getSubscriptionHistory();
            setSubscriptionHistory(history);
        } catch (error) {
            console.error('获取订阅历史失败:', error);
        }
    };
    const fetchUsageInfo = async ()=>{
        try {
            const usage = await _services.SubscriptionService.getUsageInfo();
            setUsageInfo(usage);
        } catch (error) {
            console.error('获取使用情况失败:', error);
        }
    };
    const handleRenew = async ()=>{
        if (!currentSubscription) return;
        try {
            await _services.SubscriptionService.renewSubscription(currentSubscription.id);
            _antd.message.success('续费成功！');
            onRefresh();
        } catch (error) {
            console.error('续费失败:', error);
        }
    };
    const handleCancel = async ()=>{
        if (!currentSubscription) return;
        _antd.Modal.confirm({
            title: '确认取消订阅',
            content: '取消订阅后，您将失去当前套餐的所有权益。确定要取消吗？',
            okText: '确认取消',
            cancelText: '我再想想',
            okType: 'danger',
            onOk: async ()=>{
                try {
                    await _services.SubscriptionService.cancelSubscription(currentSubscription.id);
                    _antd.message.success('订阅已取消');
                    onRefresh();
                } catch (error) {
                    console.error('取消订阅失败:', error);
                }
            }
        });
    };
    const getStatusTag = (status)=>{
        const statusMap = {
            [_api.SubscriptionStatus.ACTIVE]: {
                color: 'green',
                text: '有效'
            },
            [_api.SubscriptionStatus.EXPIRED]: {
                color: 'red',
                text: '已过期'
            },
            [_api.SubscriptionStatus.CANCELLED]: {
                color: 'default',
                text: '已取消'
            },
            [_api.SubscriptionStatus.PENDING]: {
                color: 'orange',
                text: '待激活'
            }
        };
        const config = statusMap[status] || {
            color: 'default',
            text: '未知'
        };
        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
            color: config.color,
            children: config.text
        }, void 0, false, {
            fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
            lineNumber: 115,
            columnNumber: 12
        }, this);
    };
    const historyColumns = [
        {
            title: '套餐名称',
            dataIndex: [
                'plan',
                'name'
            ],
            key: 'planName'
        },
        {
            title: '价格',
            dataIndex: 'totalPrice',
            key: 'totalPrice',
            render: (price)=>`¥${price}`
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            render: (status)=>getStatusTag(status)
        },
        {
            title: '开始时间',
            dataIndex: 'startDate',
            key: 'startDate',
            render: (date)=>new Date(date).toLocaleDateString()
        },
        {
            title: '结束时间',
            dataIndex: 'endDate',
            key: 'endDate',
            render: (date)=>new Date(date).toLocaleDateString()
        }
    ];
    if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: "加载中..."
    }, void 0, false, {
        fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
        lineNumber: 151,
        columnNumber: 12
    }, this);
    if (!currentSubscription) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
        image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
        description: "您还没有任何订阅",
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
            type: "secondary",
            children: '请前往"套餐选择"页面选择适合您的订阅套餐'
        }, void 0, false, {
            fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
            lineNumber: 160,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
        lineNumber: 156,
        columnNumber: 7
    }, this);
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    marginBottom: 24
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            marginBottom: 16
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                level: 4,
                                style: {
                                    margin: 0
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                        fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                                        lineNumber: 173,
                                        columnNumber: 13
                                    }, this),
                                    " 当前订阅"
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                                lineNumber: 172,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ReloadOutlined, {}, void 0, false, {
                                            fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                                            lineNumber: 177,
                                            columnNumber: 21
                                        }, void 0),
                                        onClick: onRefresh,
                                        children: "刷新"
                                    }, void 0, false, {
                                        fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                                        lineNumber: 176,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.HistoryOutlined, {}, void 0, false, {
                                            fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                                            lineNumber: 183,
                                            columnNumber: 21
                                        }, void 0),
                                        onClick: ()=>setHistoryModalVisible(true),
                                        children: "订阅历史"
                                    }, void 0, false, {
                                        fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                                        lineNumber: 182,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                                lineNumber: 175,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                        lineNumber: 171,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions, {
                        column: 2,
                        bordered: true,
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                label: "套餐名称",
                                children: currentSubscription.plan.name
                            }, void 0, false, {
                                fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                                lineNumber: 192,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                label: "订阅状态",
                                children: getStatusTag(currentSubscription.status)
                            }, void 0, false, {
                                fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                                lineNumber: 195,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                label: "开始时间",
                                children: new Date(currentSubscription.startDate).toLocaleDateString()
                            }, void 0, false, {
                                fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                                lineNumber: 198,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                label: "结束时间",
                                children: new Date(currentSubscription.endDate).toLocaleDateString()
                            }, void 0, false, {
                                fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                                lineNumber: 201,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                label: "总价格",
                                children: [
                                    "¥",
                                    currentSubscription.totalPrice
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                                lineNumber: 204,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                label: "存储上限",
                                children: [
                                    currentSubscription.plan.maxSize,
                                    "GB"
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                                lineNumber: 207,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                        lineNumber: 191,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            marginTop: 16
                        },
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            children: [
                                currentSubscription.status === _api.SubscriptionStatus.ACTIVE && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "primary",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UpOutlined, {}, void 0, false, {
                                                fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                                                lineNumber: 219,
                                                columnNumber: 25
                                            }, void 0),
                                            onClick: handleRenew,
                                            children: "续费"
                                        }, void 0, false, {
                                            fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                                            lineNumber: 217,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            danger: true,
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.StopOutlined, {}, void 0, false, {
                                                fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                                                lineNumber: 226,
                                                columnNumber: 25
                                            }, void 0),
                                            onClick: handleCancel,
                                            children: "取消订阅"
                                        }, void 0, false, {
                                            fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                                            lineNumber: 224,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true),
                                currentSubscription.status === _api.SubscriptionStatus.EXPIRED && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "primary",
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ReloadOutlined, {}, void 0, false, {
                                        fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                                        lineNumber: 236,
                                        columnNumber: 23
                                    }, void 0),
                                    onClick: handleRenew,
                                    children: "重新订阅"
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                                    lineNumber: 234,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                            lineNumber: 214,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                        lineNumber: 213,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                lineNumber: 170,
                columnNumber: 7
            }, this),
            usageInfo && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    marginBottom: 24
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                        level: 4,
                        children: "使用情况"
                    }, void 0, false, {
                        fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                        lineNumber: 249,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            marginBottom: 16
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                children: "存储使用量"
                            }, void 0, false, {
                                fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                                lineNumber: 251,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Progress, {
                                percent: Math.round(usageInfo.usedStorage / usageInfo.totalStorage * 100),
                                format: ()=>`${usageInfo.usedStorage}GB / ${usageInfo.totalStorage}GB`,
                                style: {
                                    marginTop: 8
                                }
                            }, void 0, false, {
                                fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                                lineNumber: 252,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                        lineNumber: 250,
                        columnNumber: 11
                    }, this),
                    usageInfo.usedStorage / usageInfo.totalStorage > 0.8 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                        message: "存储空间不足",
                        description: "您的存储空间使用量已超过80%，建议及时清理或升级套餐。",
                        type: "warning",
                        showIcon: true,
                        style: {
                            marginTop: 16
                        }
                    }, void 0, false, {
                        fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                        lineNumber: 260,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                lineNumber: 248,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "订阅历史",
                open: historyModalVisible,
                onCancel: ()=>setHistoryModalVisible(false),
                footer: null,
                width: 800,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                    columns: historyColumns,
                    dataSource: subscriptionHistory,
                    rowKey: "id",
                    pagination: {
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total)=>`共 ${total} 条记录`
                    }
                }, void 0, false, {
                    fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                    lineNumber: 279,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
                lineNumber: 272,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/subscription/components/SubscriptionManageContent.tsx",
        lineNumber: 168,
        columnNumber: 5
    }, this);
};
_s(SubscriptionManageContent, "k+fjMDmSvTMidzG8NAkujS6f/0g=");
_c = SubscriptionManageContent;
var _default = SubscriptionManageContent;
var _c;
$RefreshReg$(_c, "SubscriptionManageContent");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/subscription/components/SubscriptionPlansContent.tsx": function (module, exports, __mako_require__){
/**
 * 订阅套餐内容组件
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
const SubscriptionPlansContent = ({ onSubscriptionSuccess })=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(true);
    const [plans, setPlans] = (0, _react.useState)([]);
    const [subscribing, setSubscribing] = (0, _react.useState)(false);
    const [selectedPlan, setSelectedPlan] = (0, _react.useState)(null);
    const [subscribeModalVisible, setSubscribeModalVisible] = (0, _react.useState)(false);
    const [duration, setDuration] = (0, _react.useState)(1);
    (0, _react.useEffect)(()=>{
        fetchPlans();
    }, []);
    const fetchPlans = async ()=>{
        try {
            setLoading(true);
            const planList = await _services.SubscriptionService.getActivePlans();
            setPlans(planList);
        } catch (error) {
            console.error('获取订阅套餐失败:', error);
            _antd.message.error('获取订阅套餐失败');
        } finally{
            setLoading(false);
        }
    };
    const handleSubscribe = (plan)=>{
        setSelectedPlan(plan);
        setDuration(1);
        setSubscribeModalVisible(true);
    };
    const handleConfirmSubscribe = async ()=>{
        if (!selectedPlan) return;
        try {
            setSubscribing(true);
            const request = {
                planId: selectedPlan.id,
                duration
            };
            await _services.SubscriptionService.createSubscription(request);
            setSubscribeModalVisible(false);
            _antd.message.success('订阅成功！');
            onSubscriptionSuccess === null || onSubscriptionSuccess === void 0 || onSubscriptionSuccess();
        } catch (error) {
            console.error('订阅失败:', error);
        } finally{
            setSubscribing(false);
        }
    };
    const getPlanFeatures = (plan)=>{
        const features = [
            `数据存储上限：${plan.maxSize}GB`,
            '7x24小时技术支持',
            '数据备份与恢复',
            '团队协作功能'
        ];
        if (plan.price > 0) {
            features.push('高级分析报告');
            features.push('API 访问权限');
        }
        if (plan.price >= 100) {
            features.push('专属客户经理');
            features.push('定制化功能');
            features.push('优先技术支持');
        }
        return features;
    };
    const getPlanColor = (plan)=>{
        if (plan.price === 0) return '#52c41a'; // 免费版 - 绿色
        if (plan.price < 100) return '#1890ff'; // 基础版 - 蓝色
        return '#722ed1'; // 专业版 - 紫色
    };
    const getPlanIcon = (plan)=>{
        if (plan.price === 0) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {}, void 0, false, {
            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
            lineNumber: 117,
            columnNumber: 34
        }, this);
        if (plan.price < 100) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.StarOutlined, {}, void 0, false, {
            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
            lineNumber: 118,
            columnNumber: 34
        }, this);
        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
            lineNumber: 119,
            columnNumber: 12
        }, this);
    };
    const calculatePrice = (plan, months)=>{
        return _services.SubscriptionService.calculatePlanPrice(plan, months);
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                gutter: [
                    24,
                    24
                ],
                children: plans.map((plan)=>{
                    const features = getPlanFeatures(plan);
                    const color = getPlanColor(plan);
                    const icon = getPlanIcon(plan);
                    const isPopular = plan.price > 0 && plan.price < 100;
                    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        xs: 24,
                        sm: 12,
                        lg: 8,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            hoverable: true,
                            loading: loading,
                            style: {
                                height: '100%',
                                borderColor: isPopular ? '#1890ff' : undefined,
                                position: 'relative'
                            },
                            children: [
                                isPopular && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        position: 'absolute',
                                        top: -1,
                                        right: 24,
                                        background: '#1890ff',
                                        color: 'white',
                                        padding: '4px 12px',
                                        borderRadius: '0 0 8px 8px',
                                        fontSize: 12,
                                        fontWeight: 'bold'
                                    },
                                    children: "推荐"
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                    lineNumber: 147,
                                    columnNumber: 19
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        textAlign: 'center',
                                        marginBottom: 24
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                fontSize: 48,
                                                color,
                                                marginBottom: 16
                                            },
                                            children: icon
                                        }, void 0, false, {
                                            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                            lineNumber: 165,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                            level: 3,
                                            style: {
                                                margin: 0,
                                                color
                                            },
                                            children: plan.name
                                        }, void 0, false, {
                                            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                            lineNumber: 168,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            type: "secondary",
                                            children: plan.description
                                        }, void 0, false, {
                                            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                            lineNumber: 171,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                    lineNumber: 164,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        textAlign: 'center',
                                        marginBottom: 24
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                fontSize: 36,
                                                fontWeight: 'bold',
                                                color
                                            },
                                            children: [
                                                "¥",
                                                plan.price,
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                    style: {
                                                        fontSize: 16,
                                                        fontWeight: 'normal'
                                                    },
                                                    children: "/月"
                                                }, void 0, false, {
                                                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                    lineNumber: 177,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                            lineNumber: 175,
                                            columnNumber: 19
                                        }, this),
                                        plan.price === 0 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                            color: "green",
                                            style: {
                                                marginTop: 8
                                            },
                                            children: "永久免费"
                                        }, void 0, false, {
                                            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                            lineNumber: 180,
                                            columnNumber: 21
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                    lineNumber: 174,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                                    size: "small",
                                    dataSource: features,
                                    renderItem: (feature)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                    style: {
                                                        color: '#52c41a',
                                                        marginRight: 8
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                    lineNumber: 191,
                                                    columnNumber: 23
                                                }, void 0),
                                                feature
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                            lineNumber: 190,
                                            columnNumber: 21
                                        }, void 0),
                                    style: {
                                        marginBottom: 24
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                    lineNumber: 186,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: isPopular ? 'primary' : 'default',
                                    size: "large",
                                    block: true,
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ShoppingCartOutlined, {}, void 0, false, {
                                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                        lineNumber: 202,
                                        columnNumber: 25
                                    }, void 0),
                                    onClick: ()=>handleSubscribe(plan),
                                    disabled: plan.price === 0,
                                    children: plan.price === 0 ? '当前套餐' : '立即订阅'
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                    lineNumber: 198,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                            lineNumber: 137,
                            columnNumber: 15
                        }, this)
                    }, plan.id, false, {
                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                        lineNumber: 136,
                        columnNumber: 13
                    }, this);
                })
            }, void 0, false, {
                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                lineNumber: 128,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                title: "套餐对比",
                style: {
                    marginTop: 32
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        overflowX: 'auto'
                    },
                    children: plans.length > 0 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        children: _services.SubscriptionService.comparePlans(plans).map((comparison, index)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                style: {
                                    padding: '12px 0',
                                    borderBottom: '1px solid #f0f0f0'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        span: 6,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: comparison.feature
                                        }, void 0, false, {
                                            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                            lineNumber: 222,
                                            columnNumber: 21
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                        lineNumber: 221,
                                        columnNumber: 19
                                    }, this),
                                    comparison.values.map((value, valueIndex)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            span: 6,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                children: typeof value === 'boolean' ? value ? '✓' : '✗' : value
                                            }, void 0, false, {
                                                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                lineNumber: 226,
                                                columnNumber: 23
                                            }, this)
                                        }, `value-${index}-${valueIndex}`, false, {
                                            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                            lineNumber: 225,
                                            columnNumber: 21
                                        }, this))
                                ]
                            }, `comparison-${index}`, true, {
                                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                lineNumber: 220,
                                columnNumber: 17
                            }, this))
                    }, void 0, false, {
                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                        lineNumber: 218,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                    lineNumber: 216,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                lineNumber: 215,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "确认订阅",
                open: subscribeModalVisible,
                onCancel: ()=>setSubscribeModalVisible(false),
                footer: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        onClick: ()=>setSubscribeModalVisible(false),
                        children: "取消"
                    }, "cancel", false, {
                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                        lineNumber: 244,
                        columnNumber: 11
                    }, void 0),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "primary",
                        loading: subscribing,
                        onClick: handleConfirmSubscribe,
                        children: "确认订阅"
                    }, "confirm", false, {
                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                        lineNumber: 247,
                        columnNumber: 11
                    }, void 0)
                ],
                children: selectedPlan && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginBottom: 16
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    strong: true,
                                    children: "套餐："
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                    lineNumber: 260,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    children: selectedPlan.name
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                    lineNumber: 261,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                            lineNumber: 259,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginBottom: 16
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    strong: true,
                                    children: "订阅时长："
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                    lineNumber: 265,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.InputNumber, {
                                    min: 1,
                                    max: 24,
                                    value: duration,
                                    onChange: (value)=>setDuration(value || 1),
                                    addonAfter: "个月",
                                    style: {
                                        marginLeft: 8
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                    lineNumber: 266,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                            lineNumber: 264,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                            lineNumber: 276,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    strong: true,
                                    children: "价格详情："
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                    lineNumber: 279,
                                    columnNumber: 15
                                }, this),
                                (()=>{
                                    const priceInfo = calculatePrice(selectedPlan, duration);
                                    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        style: {
                                            marginTop: 8
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                children: [
                                                    "原价：¥",
                                                    priceInfo.originalPrice
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                lineNumber: 284,
                                                columnNumber: 21
                                            }, this),
                                            priceInfo.discount > 0 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    color: '#ff4d4f'
                                                },
                                                children: [
                                                    "折扣：-",
                                                    priceInfo.discount,
                                                    "%"
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                lineNumber: 286,
                                                columnNumber: 23
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    fontSize: 18,
                                                    fontWeight: 'bold',
                                                    color: '#1890ff'
                                                },
                                                children: [
                                                    "总计：¥",
                                                    priceInfo.totalPrice
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                lineNumber: 290,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                        lineNumber: 283,
                                        columnNumber: 19
                                    }, this);
                                })()
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                            lineNumber: 278,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                    lineNumber: 258,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                lineNumber: 239,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
        lineNumber: 127,
        columnNumber: 5
    }, this);
};
_s(SubscriptionPlansContent, "r8YRf6YYs6oegStol4umzcvp5Ls=");
_c = SubscriptionPlansContent;
var _default = SubscriptionPlansContent;
var _c;
$RefreshReg$(_c, "SubscriptionPlansContent");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/team/components/TeamListContent.tsx": function (module, exports, __mako_require__){
/**
 * 团队列表内容组件
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
const { Search } = _antd.Input;
const TeamListContent = ()=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(true);
    const [teams, setTeams] = (0, _react.useState)([]);
    const [filteredTeams, setFilteredTeams] = (0, _react.useState)([]);
    const [searchText, setSearchText] = (0, _react.useState)('');
    (0, _react.useEffect)(()=>{
        fetchTeams();
    }, []);
    (0, _react.useEffect)(()=>{
        const filtered = teams.filter((team)=>team.name.toLowerCase().includes(searchText.toLowerCase()) || team.description && team.description.toLowerCase().includes(searchText.toLowerCase()));
        setFilteredTeams(filtered);
    }, [
        teams,
        searchText
    ]);
    const fetchTeams = async ()=>{
        try {
            setLoading(true);
            const teamList = await _services.TeamService.getUserTeams();
            setTeams(teamList);
        } catch (error) {
            console.error('获取团队列表失败:', error);
            _antd.message.error('获取团队列表失败');
        } finally{
            setLoading(false);
        }
    };
    const handleCreateTeam = ()=>{
        _max.history.push('/team/create');
    };
    const handleViewTeam = async (team)=>{
        try {
            const response = await _services.AuthService.teamLogin({
                teamId: team.id
            });
            if (response.teamSelectionSuccess && response.team && response.team.id === team.id) _antd.message.success(`已切换到团队：${team.name}`);
            else {
                console.error('团队切换响应异常，未返回正确的团队信息');
                _antd.message.error('团队切换失败，请重试');
            }
        } catch (error) {
            console.error('切换团队失败:', error);
            _antd.message.error('切换团队失败');
        }
    };
    const handleSwitchTeam = async (team)=>{
        try {
            const response = await _services.AuthService.teamLogin({
                teamId: team.id
            });
            if (response.teamSelectionSuccess && response.team && response.team.id === team.id) {
                _antd.message.success(`已切换到团队：${team.name}`);
                setTimeout(()=>{
                    _max.history.push('/');
                }, 200);
            } else {
                console.error('团队切换响应异常，未返回正确的团队信息');
                _antd.message.error('团队切换失败，请重试');
            }
        } catch (error) {
            console.error('切换团队失败:', error);
            _antd.message.error('切换团队失败');
        }
    };
    return (0, _jsxdevruntime.jsxDEV)("div", {
        children: [
            (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    marginBottom: 16
                },
                children: (0, _jsxdevruntime.jsxDEV)(Search, {
                    placeholder: "搜索团队名称或描述",
                    allowClear: true,
                    value: searchText,
                    onChange: (e)=>setSearchText(e.target.value),
                    style: {
                        width: 300
                    }
                }, void 0, false, {
                    fileName: "src/pages/team/components/TeamListContent.tsx",
                    lineNumber: 111,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team/components/TeamListContent.tsx",
                lineNumber: 110,
                columnNumber: 7
            }, this),
            filteredTeams.length === 0 && !loading ? (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
                image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
                description: searchText ? '没有找到匹配的团队' : '您还没有加入任何团队',
                children: !searchText && (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    type: "primary",
                    onClick: handleCreateTeam,
                    children: "创建第一个团队"
                }, void 0, false, {
                    fileName: "src/pages/team/components/TeamListContent.tsx",
                    lineNumber: 128,
                    columnNumber: 13
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team/components/TeamListContent.tsx",
                lineNumber: 121,
                columnNumber: 9
            }, this) : (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                loading: loading,
                itemLayout: "horizontal",
                dataSource: filteredTeams,
                pagination: {
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total)=>`共 ${total} 个团队`
                },
                renderItem: (team)=>(0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                        actions: [
                            (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "text",
                                icon: (0, _jsxdevruntime.jsxDEV)(_icons.EyeOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                    lineNumber: 149,
                                    columnNumber: 25
                                }, void 0),
                                onClick: ()=>handleViewTeam(team),
                                children: "查看详情"
                            }, "view", false, {
                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                lineNumber: 146,
                                columnNumber: 17
                            }, void 0),
                            (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "primary",
                                size: "small",
                                onClick: ()=>handleSwitchTeam(team),
                                children: "进入团队"
                            }, "switch", false, {
                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                lineNumber: 154,
                                columnNumber: 17
                            }, void 0)
                        ],
                        children: (0, _jsxdevruntime.jsxDEV)(_antd.List.Item.Meta, {
                            avatar: (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                size: 64,
                                icon: (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                    lineNumber: 168,
                                    columnNumber: 27
                                }, void 0),
                                style: {
                                    backgroundColor: '#1890ff'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                lineNumber: 166,
                                columnNumber: 19
                            }, void 0),
                            title: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    (0, _jsxdevruntime.jsxDEV)(Title, {
                                        level: 4,
                                        style: {
                                            margin: 0
                                        },
                                        children: team.name
                                    }, void 0, false, {
                                        fileName: "src/pages/team/components/TeamListContent.tsx",
                                        lineNumber: 174,
                                        columnNumber: 21
                                    }, void 0),
                                    team.isCreator && (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                        color: "gold",
                                        icon: (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                            lineNumber: 178,
                                            columnNumber: 47
                                        }, void 0),
                                        children: "创建者"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/components/TeamListContent.tsx",
                                        lineNumber: 178,
                                        columnNumber: 23
                                    }, void 0)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                lineNumber: 173,
                                columnNumber: 19
                            }, void 0),
                            description: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                direction: "vertical",
                                size: "small",
                                children: [
                                    team.description && (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: team.description
                                    }, void 0, false, {
                                        fileName: "src/pages/team/components/TeamListContent.tsx",
                                        lineNumber: 187,
                                        columnNumber: 23
                                    }, void 0),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                        children: [
                                            (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                size: "small",
                                                children: [
                                                    (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team/components/TeamListContent.tsx",
                                                        lineNumber: 191,
                                                        columnNumber: 25
                                                    }, void 0),
                                                    (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        type: "secondary",
                                                        children: [
                                                            team.memberCount,
                                                            " 名成员"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/team/components/TeamListContent.tsx",
                                                        lineNumber: 192,
                                                        columnNumber: 25
                                                    }, void 0)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                                lineNumber: 190,
                                                columnNumber: 23
                                            }, void 0),
                                            (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                children: [
                                                    "创建于 ",
                                                    new Date(team.createdAt).toLocaleDateString()
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                                lineNumber: 194,
                                                columnNumber: 23
                                            }, void 0),
                                            (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                children: [
                                                    "更新于 ",
                                                    new Date(team.updatedAt).toLocaleDateString()
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                                lineNumber: 197,
                                                columnNumber: 23
                                            }, void 0)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team/components/TeamListContent.tsx",
                                        lineNumber: 189,
                                        columnNumber: 21
                                    }, void 0)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                lineNumber: 185,
                                columnNumber: 19
                            }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/team/components/TeamListContent.tsx",
                            lineNumber: 164,
                            columnNumber: 15
                        }, void 0)
                    }, void 0, false, {
                        fileName: "src/pages/team/components/TeamListContent.tsx",
                        lineNumber: 144,
                        columnNumber: 13
                    }, void 0)
            }, void 0, false, {
                fileName: "src/pages/team/components/TeamListContent.tsx",
                lineNumber: 134,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/team/components/TeamListContent.tsx",
        lineNumber: 109,
        columnNumber: 5
    }, this);
};
_s(TeamListContent, "6oD2tIqxbPJXnbsp9fetgXShQMo=");
_c = TeamListContent;
var _default = TeamListContent;
var _c;
$RefreshReg$(_c, "TeamListContent");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/team/detail/components/InviteMemberModal.tsx": function (module, exports, __mako_require__){
/**
 * 邀请成员模态框组件
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text } = _antd.Typography;
const InviteMemberModal = ({ visible, onCancel, onSuccess })=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(false);
    const [emails, setEmails] = (0, _react.useState)([
        ''
    ]);
    const [form] = _antd.Form.useForm();
    const [inviteResults, setInviteResults] = (0, _react.useState)([]);
    const [showResults, setShowResults] = (0, _react.useState)(false);
    const handleAddEmail = ()=>{
        if (emails.length < 10) setEmails([
            ...emails,
            ''
        ]);
        else _antd.message.warning('一次最多邀请10个成员');
    };
    const handleRemoveEmail = (index)=>{
        if (emails.length > 1) {
            const newEmails = emails.filter((_, i)=>i !== index);
            setEmails(newEmails);
        }
    };
    const handleEmailChange = (index, value)=>{
        const newEmails = [
            ...emails
        ];
        newEmails[index] = value;
        setEmails(newEmails);
    };
    const validateEmail = (email)=>{
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };
    const handleSubmit = async ()=>{
        // 过滤空邮箱并验证格式
        const validEmails = emails.filter((email)=>email.trim() !== '');
        if (validEmails.length === 0) {
            _antd.message.warning('请至少输入一个邮箱地址');
            return;
        }
        // 验证邮箱格式
        const invalidEmails = validEmails.filter((email)=>!validateEmail(email));
        if (invalidEmails.length > 0) {
            _antd.message.error(`以下邮箱格式不正确: ${invalidEmails.join(', ')}`);
            return;
        }
        // 检查重复邮箱
        const uniqueEmails = [
            ...new Set(validEmails)
        ];
        if (uniqueEmails.length !== validEmails.length) {
            _antd.message.warning('存在重复的邮箱地址');
            return;
        }
        try {
            setLoading(true);
            setShowResults(false);
            const request = {
                emails: uniqueEmails
            };
            await _services.TeamService.inviteMembers(request);
            // 模拟邀请结果（实际应该从后端返回）
            const results = uniqueEmails.map((email)=>({
                    email,
                    success: true
                }));
            setInviteResults(results);
            setShowResults(true);
            _antd.message.success(`成功发送 ${uniqueEmails.length} 份邀请`);
            // 延迟关闭模态框，让用户看到结果
            setTimeout(()=>{
                onSuccess();
                handleCancel();
            }, 2000);
        } catch (error) {
            console.error('邀请成员失败:', error);
            // 处理部分成功的情况
            const results = uniqueEmails.map((email)=>({
                    email,
                    success: false,
                    error: error.message || '邀请失败'
                }));
            setInviteResults(results);
            setShowResults(true);
            _antd.message.error('邀请发送失败');
        } finally{
            setLoading(false);
        }
    };
    const handleCancel = ()=>{
        setEmails([
            ''
        ]);
        setInviteResults([]);
        setShowResults(false);
        form.resetFields();
        onCancel();
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
        title: "邀请团队成员",
        open: visible,
        onCancel: handleCancel,
        footer: showResults ? [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                type: "primary",
                onClick: handleCancel,
                children: "关闭"
            }, "close", false, {
                fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                lineNumber: 166,
                columnNumber: 11
            }, void 0)
        ] : [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                onClick: handleCancel,
                children: "取消"
            }, "cancel", false, {
                fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                lineNumber: 170,
                columnNumber: 11
            }, void 0),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                type: "primary",
                loading: loading,
                onClick: handleSubmit,
                disabled: emails.filter((email)=>email.trim() !== '').length === 0,
                children: "发送邀请"
            }, "submit", false, {
                fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                lineNumber: 173,
                columnNumber: 11
            }, void 0)
        ],
        width: 700,
        children: [
            !showResults ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            marginBottom: 16
                        },
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "secondary",
                            children: "输入要邀请的成员邮箱地址，系统将发送邀请邮件给他们。"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                            lineNumber: 189,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                        lineNumber: 188,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                        message: "邀请说明",
                        description: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("ul", {
                            style: {
                                marginTop: 8,
                                paddingLeft: 20,
                                marginBottom: 0
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                    children: "邀请邮件将发送到指定邮箱"
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                                    lineNumber: 198,
                                    columnNumber: 17
                                }, void 0),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                    children: "受邀者需要注册账号后才能加入团队"
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                                    lineNumber: 199,
                                    columnNumber: 17
                                }, void 0),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                    children: "一次最多可邀请10个成员"
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                                    lineNumber: 200,
                                    columnNumber: 17
                                }, void 0),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                    children: "邀请链接有效期为7天"
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                                    lineNumber: 201,
                                    columnNumber: 17
                                }, void 0)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                            lineNumber: 197,
                            columnNumber: 15
                        }, void 0),
                        type: "info",
                        showIcon: true,
                        style: {
                            marginBottom: 16
                        }
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                        lineNumber: 194,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    marginBottom: 16
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                        message: "邀请结果",
                        description: `已处理 ${inviteResults.length} 个邀请`,
                        type: inviteResults.every((r)=>r.success) ? 'success' : 'warning',
                        showIcon: true,
                        style: {
                            marginBottom: 16
                        }
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                        lineNumber: 211,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                        size: "small",
                        dataSource: inviteResults,
                        renderItem: (result)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                    children: [
                                        result.success ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckCircleOutlined, {
                                            style: {
                                                color: '#52c41a'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                                            lineNumber: 226,
                                            columnNumber: 21
                                        }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                            style: {
                                                color: '#faad14'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                                            lineNumber: 228,
                                            columnNumber: 21
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            children: result.email
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                                            lineNumber: 230,
                                            columnNumber: 19
                                        }, void 0),
                                        result.success ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                            color: "success",
                                            children: "邀请已发送"
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                                            lineNumber: 232,
                                            columnNumber: 21
                                        }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                            title: result.error,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                color: "warning",
                                                children: "发送失败"
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                                                lineNumber: 235,
                                                columnNumber: 23
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                                            lineNumber: 234,
                                            columnNumber: 21
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                                    lineNumber: 224,
                                    columnNumber: 17
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                                lineNumber: 223,
                                columnNumber: 15
                            }, void 0)
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                        lineNumber: 219,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                lineNumber: 210,
                columnNumber: 9
            }, this),
            !showResults && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                form: form,
                layout: "vertical",
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                        label: "邀请邮箱",
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            direction: "vertical",
                            style: {
                                width: '100%'
                            },
                            children: emails.map((email, index)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                    style: {
                                        width: '100%'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                            placeholder: "请输入邮箱地址",
                                            value: email,
                                            onChange: (e)=>handleEmailChange(index, e.target.value),
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                                                lineNumber: 255,
                                                columnNumber: 29
                                            }, void 0),
                                            style: {
                                                flex: 1
                                            },
                                            status: email && !validateEmail(email) ? 'error' : ''
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                                            lineNumber: 251,
                                            columnNumber: 19
                                        }, this),
                                        emails.length > 1 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "text",
                                            danger: true,
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                                                lineNumber: 263,
                                                columnNumber: 29
                                            }, void 0),
                                            onClick: ()=>handleRemoveEmail(index)
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                                            lineNumber: 260,
                                            columnNumber: 21
                                        }, this)
                                    ]
                                }, index, true, {
                                    fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                                    lineNumber: 250,
                                    columnNumber: 17
                                }, this))
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                            lineNumber: 248,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                        lineNumber: 247,
                        columnNumber: 11
                    }, this),
                    emails.length < 10 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "dashed",
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                            lineNumber: 275,
                            columnNumber: 21
                        }, void 0),
                        onClick: handleAddEmail,
                        style: {
                            width: '100%',
                            marginBottom: 16
                        },
                        children: "添加更多邮箱"
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                        lineNumber: 273,
                        columnNumber: 13
                    }, this),
                    emails.filter((email)=>email.trim() !== '').length > 0 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            marginTop: 16
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                strong: true,
                                children: "待邀请邮箱："
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                                lineNumber: 285,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    marginTop: 8
                                },
                                children: emails.filter((email)=>email.trim() !== '').map((email, index)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                        color: validateEmail(email) ? 'blue' : 'red',
                                        style: {
                                            marginBottom: 4
                                        },
                                        children: email
                                    }, index, false, {
                                        fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                                        lineNumber: 290,
                                        columnNumber: 21
                                    }, this))
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                                lineNumber: 286,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                        lineNumber: 284,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
                lineNumber: 246,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/team/detail/components/InviteMemberModal.tsx",
        lineNumber: 160,
        columnNumber: 5
    }, this);
};
_s(InviteMemberModal, "7+51RTEemHZ4L4c6IwA5mTmNqws=", false, function() {
    return [
        _antd.Form.useForm
    ];
});
_c = InviteMemberModal;
var _default = InviteMemberModal;
var _c;
$RefreshReg$(_c, "InviteMemberModal");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/team/detail/components/MemberAssignModal.tsx": function (module, exports, __mako_require__){
/**
 * 成员分配模态框组件
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text } = _antd.Typography;
const { Option } = _antd.Select;
const MemberAssignModal = ({ visible, onCancel, onSuccess, currentTeamId })=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(false);
    const [members, setMembers] = (0, _react.useState)([]);
    const [availableTargets, setAvailableTargets] = (0, _react.useState)([]);
    const [selectedMembers, setSelectedMembers] = (0, _react.useState)([]);
    const [selectedTarget, setSelectedTarget] = (0, _react.useState)();
    const [form] = _antd.Form.useForm();
    (0, _react.useEffect)(()=>{
        if (visible) {
            fetchMembers();
            fetchAvailableTargets();
        }
    }, [
        visible
    ]);
    const fetchMembers = async ()=>{
        try {
            const response = await _services.TeamService.getTeamMembers({
                current: 1,
                pageSize: 1000
            });
            // 过滤掉创建者，因为创建者不能被分配
            const assignableMembers = response.list.filter((member)=>!member.isCreator);
            setMembers(assignableMembers);
        } catch (error) {
            console.error('获取团队成员失败:', error);
            _antd.message.error('获取团队成员失败');
        }
    };
    const fetchAvailableTargets = async ()=>{
        try {
            // 获取用户的其他团队作为分配目标
            const teams = await _services.TeamService.getUserTeams();
            const otherTeams = teams.filter((team)=>team.id !== currentTeamId).map((team)=>({
                    id: team.id,
                    name: team.name,
                    type: 'team',
                    memberCount: team.memberCount
                }));
            setAvailableTargets(otherTeams);
        } catch (error) {
            console.error('获取可分配目标失败:', error);
            _antd.message.error('获取可分配目标失败');
        }
    };
    const handleAssign = async ()=>{
        if (selectedMembers.length === 0) {
            _antd.message.warning('请选择要分配的成员');
            return;
        }
        if (!selectedTarget) {
            _antd.message.warning('请选择分配目标');
            return;
        }
        try {
            setLoading(true);
            // 这里应该调用后端API进行成员分配
            // 由于后端可能没有这个接口，我们模拟一个成功的响应
            await new Promise((resolve)=>setTimeout(resolve, 1000));
            _antd.message.success(`成功将 ${selectedMembers.length} 名成员分配到目标团队`);
            onSuccess();
            handleCancel();
        } catch (error) {
            console.error('分配成员失败:', error);
            _antd.message.error('分配成员失败');
        } finally{
            setLoading(false);
        }
    };
    const handleCancel = ()=>{
        setSelectedMembers([]);
        setSelectedTarget(undefined);
        form.resetFields();
        onCancel();
    };
    const selectedMemberDetails = members.filter((member)=>selectedMembers.includes(member.id));
    const selectedTargetDetails = availableTargets.find((target)=>target.id === selectedTarget);
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
        title: "分配团队成员",
        open: visible,
        onCancel: handleCancel,
        footer: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                onClick: handleCancel,
                children: "取消"
            }, "cancel", false, {
                fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                lineNumber: 150,
                columnNumber: 9
            }, void 0),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                type: "primary",
                loading: loading,
                onClick: handleAssign,
                disabled: selectedMembers.length === 0 || !selectedTarget,
                children: "确认分配"
            }, "submit", false, {
                fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                lineNumber: 153,
                columnNumber: 9
            }, void 0)
        ],
        width: 800,
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                message: "成员分配说明",
                description: "将选中的成员从当前团队分配到其他团队。分配后，成员将离开当前团队并加入目标团队。",
                type: "info",
                showIcon: true,
                style: {
                    marginBottom: 16
                }
            }, void 0, false, {
                fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                lineNumber: 165,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                form: form,
                layout: "vertical",
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                        label: "选择要分配的成员",
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                maxHeight: 200,
                                overflowY: 'auto',
                                border: '1px solid #d9d9d9',
                                borderRadius: 6,
                                padding: 8
                            },
                            children: members.length === 0 ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                children: "暂无可分配的成员"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                lineNumber: 177,
                                columnNumber: 15
                            }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                                size: "small",
                                dataSource: members,
                                renderItem: (member)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                        style: {
                                            cursor: 'pointer',
                                            backgroundColor: selectedMembers.includes(member.id) ? '#e6f7ff' : 'transparent',
                                            padding: '8px 12px',
                                            borderRadius: 4,
                                            margin: '2px 0'
                                        },
                                        onClick: ()=>{
                                            if (selectedMembers.includes(member.id)) setSelectedMembers((prev)=>prev.filter((id)=>id !== member.id));
                                            else setSelectedMembers((prev)=>[
                                                    ...prev,
                                                    member.id
                                                ]);
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item.Meta, {
                                                avatar: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                                    size: "small",
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                        lineNumber: 200,
                                                        columnNumber: 58
                                                    }, void 0)
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                    lineNumber: 200,
                                                    columnNumber: 31
                                                }, void 0),
                                                title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    children: [
                                                        member.name,
                                                        selectedMembers.includes(member.id) && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckCircleOutlined, {
                                                            style: {
                                                                color: '#1890ff'
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                            lineNumber: 205,
                                                            columnNumber: 29
                                                        }, void 0)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                    lineNumber: 202,
                                                    columnNumber: 25
                                                }, void 0),
                                                description: member.email
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                lineNumber: 199,
                                                columnNumber: 21
                                            }, void 0),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                color: member.isActive ? 'green' : 'red',
                                                children: member.isActive ? '活跃' : '停用'
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                lineNumber: 211,
                                                columnNumber: 21
                                            }, void 0)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                        lineNumber: 183,
                                        columnNumber: 19
                                    }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                lineNumber: 179,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                            lineNumber: 175,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                        lineNumber: 174,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                        label: "选择分配目标",
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                            placeholder: "请选择目标团队",
                            value: selectedTarget,
                            onChange: setSelectedTarget,
                            style: {
                                width: '100%'
                            },
                            children: availableTargets.map((target)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                    value: target.id,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                lineNumber: 231,
                                                columnNumber: 19
                                            }, this),
                                            target.name,
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                children: [
                                                    "(",
                                                    target.memberCount,
                                                    " 名成员)"
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                lineNumber: 233,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                        lineNumber: 230,
                                        columnNumber: 17
                                    }, this)
                                }, target.id, false, {
                                    fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                    lineNumber: 229,
                                    columnNumber: 15
                                }, this))
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                            lineNumber: 222,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                        lineNumber: 221,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                lineNumber: 173,
                columnNumber: 7
            }, this),
            selectedMembers.length > 0 && selectedTargetDetails && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                        fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                        lineNumber: 243,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                strong: true,
                                children: "分配预览："
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                lineNumber: 245,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    marginTop: 8,
                                    padding: 12,
                                    background: '#f5f5f5',
                                    borderRadius: 6
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                    direction: "vertical",
                                    style: {
                                        width: '100%'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    children: [
                                                        "将以下 ",
                                                        selectedMemberDetails.length,
                                                        " 名成员："
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                    lineNumber: 249,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        marginTop: 4
                                                    },
                                                    children: selectedMemberDetails.map((member)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                            color: "blue",
                                                            style: {
                                                                margin: '2px'
                                                            },
                                                            children: member.name
                                                        }, member.id, false, {
                                                            fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                            lineNumber: 252,
                                                            columnNumber: 23
                                                        }, this))
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                    lineNumber: 250,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                            lineNumber: 248,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                textAlign: 'center'
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SwapOutlined, {
                                                style: {
                                                    fontSize: 16,
                                                    color: '#1890ff'
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                lineNumber: 259,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                            lineNumber: 258,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    children: "分配到团队："
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                    lineNumber: 262,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                    color: "green",
                                                    style: {
                                                        marginLeft: 8
                                                    },
                                                    children: selectedTargetDetails.name
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                    lineNumber: 263,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                            lineNumber: 261,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                    lineNumber: 247,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                lineNumber: 246,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                        lineNumber: 244,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true)
        ]
    }, void 0, true, {
        fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
        lineNumber: 145,
        columnNumber: 5
    }, this);
};
_s(MemberAssignModal, "7bJL8QxrUB07zAQJIhuLmnGERu4=", false, function() {
    return [
        _antd.Form.useForm
    ];
});
_c = MemberAssignModal;
var _default = MemberAssignModal;
var _c;
$RefreshReg$(_c, "MemberAssignModal");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/team/detail/components/TeamDetailContent.tsx": function (module, exports, __mako_require__){
/**
 * 团队详情内容组件
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var _TeamMemberList = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/detail/components/TeamMemberList.tsx"));
var _InviteMemberModal = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/detail/components/InviteMemberModal.tsx"));
var _MemberAssignModal = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/detail/components/MemberAssignModal.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
const { TextArea } = _antd.Input;
const TeamDetailContent = ({ teamDetail, loading, onRefresh })=>{
    _s();
    const [editModalVisible, setEditModalVisible] = (0, _react.useState)(false);
    const [inviteModalVisible, setInviteModalVisible] = (0, _react.useState)(false);
    const [assignModalVisible, setAssignModalVisible] = (0, _react.useState)(false);
    const [updating, setUpdating] = (0, _react.useState)(false);
    const [form] = _antd.Form.useForm();
    const handleEdit = ()=>{
        if (!teamDetail) return;
        form.setFieldsValue({
            name: teamDetail.name,
            description: teamDetail.description
        });
        setEditModalVisible(true);
    };
    const handleUpdate = async (values)=>{
        if (!teamDetail) return;
        try {
            setUpdating(true);
            const updateData = {
                name: values.name,
                description: values.description
            };
            await _services.TeamService.updateTeam(teamDetail.id, updateData);
            setEditModalVisible(false);
            _antd.message.success('团队信息更新成功');
            onRefresh();
        } catch (error) {
            console.error('更新团队信息失败:', error);
        } finally{
            setUpdating(false);
        }
    };
    if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        style: {
            textAlign: 'center',
            padding: '50px 0'
        },
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
            size: "large"
        }, void 0, false, {
            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
            lineNumber: 85,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
        lineNumber: 84,
        columnNumber: 7
    }, this);
    if (!teamDetail) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
        image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
        description: "请先选择一个团队"
    }, void 0, false, {
        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
        lineNumber: 92,
        columnNumber: 7
    }, this);
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    marginBottom: 24
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            marginBottom: 16
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                        style: {
                                            fontSize: 24,
                                            color: '#1890ff'
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 105,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                        level: 3,
                                        style: {
                                            margin: 0
                                        },
                                        children: teamDetail.name
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 106,
                                        columnNumber: 13
                                    }, this),
                                    teamDetail.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        style: {
                                            fontSize: 14
                                        },
                                        children: "(管理员)"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 110,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 104,
                                columnNumber: 11
                            }, this),
                            teamDetail.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserAddOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 119,
                                            columnNumber: 23
                                        }, void 0),
                                        onClick: ()=>setInviteModalVisible(true),
                                        children: "邀请成员"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 117,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SwapOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 125,
                                            columnNumber: 23
                                        }, void 0),
                                        onClick: ()=>setAssignModalVisible(true),
                                        children: "分配成员"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 124,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 131,
                                            columnNumber: 23
                                        }, void 0),
                                        onClick: handleEdit,
                                        children: "编辑团队"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 130,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 116,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                        lineNumber: 103,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions, {
                        column: 2,
                        bordered: true,
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                label: "团队名称",
                                children: teamDetail.name
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 141,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                label: "成员数量",
                                children: [
                                    teamDetail.memberCount,
                                    " 人"
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 144,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                label: "创建时间",
                                children: new Date(teamDetail.createdAt).toLocaleString()
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 147,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                label: "更新时间",
                                children: new Date(teamDetail.updatedAt).toLocaleString()
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 150,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                label: "团队描述",
                                span: 2,
                                children: teamDetail.description || '暂无描述'
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 153,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                        lineNumber: 140,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 102,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 159,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamMemberList.default, {
                teamId: teamDetail.id,
                isCreator: teamDetail.isCreator
            }, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 162,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "编辑团队信息",
                open: editModalVisible,
                onCancel: ()=>setEditModalVisible(false),
                footer: null,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: form,
                    layout: "vertical",
                    onFinish: handleUpdate,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队名称",
                            name: "name",
                            rules: [
                                {
                                    required: true,
                                    message: '请输入团队名称'
                                },
                                {
                                    min: 2,
                                    max: 50,
                                    message: '团队名称长度应在2-50个字符之间'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                placeholder: "请输入团队名称"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 184,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 176,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队描述",
                            name: "description",
                            rules: [
                                {
                                    max: 200,
                                    message: '团队描述不能超过200个字符'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                rows: 4,
                                placeholder: "请输入团队描述（可选）",
                                showCount: true,
                                maxLength: 200
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 194,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 187,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            style: {
                                marginBottom: 0,
                                textAlign: 'right'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: ()=>setEditModalVisible(false),
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 204,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        htmlType: "submit",
                                        loading: updating,
                                        children: "保存"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 207,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 203,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 202,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                    lineNumber: 171,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 165,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_InviteMemberModal.default, {
                visible: inviteModalVisible,
                onCancel: ()=>setInviteModalVisible(false),
                teamId: teamDetail.id,
                onSuccess: ()=>{
                    setInviteModalVisible(false);
                    onRefresh();
                }
            }, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 216,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_MemberAssignModal.default, {
                visible: assignModalVisible,
                onCancel: ()=>setAssignModalVisible(false),
                teamId: teamDetail.id,
                onSuccess: ()=>{
                    setAssignModalVisible(false);
                    onRefresh();
                }
            }, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 227,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
        lineNumber: 100,
        columnNumber: 5
    }, this);
};
_s(TeamDetailContent, "8oadfNct3csew1RHK/nTWC/j5TM=", false, function() {
    return [
        _antd.Form.useForm
    ];
});
_c = TeamDetailContent;
var _default = TeamDetailContent;
var _c;
$RefreshReg$(_c, "TeamDetailContent");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/team/detail/components/TeamMemberList.tsx": function (module, exports, __mako_require__){
/**
 * 团队成员列表组件
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text } = _antd.Typography;
const { Option } = _antd.Select;
const TeamMemberList = ({ teamId, isCreator, onMemberChange })=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(true);
    const [members, setMembers] = (0, _react.useState)([]);
    const [searchText, setSearchText] = (0, _react.useState)('');
    const [filteredMembers, setFilteredMembers] = (0, _react.useState)([]);
    const [selectedRowKeys, setSelectedRowKeys] = (0, _react.useState)([]);
    const [statusFilter, setStatusFilter] = (0, _react.useState)('all');
    (0, _react.useEffect)(()=>{
        fetchMembers();
    }, [
        teamId
    ]);
    (0, _react.useEffect)(()=>{
        // 过滤成员列表 - 添加空值检查
        if (!members || !Array.isArray(members)) {
            setFilteredMembers([]);
            return;
        }
        const filtered = members.filter((member)=>{
            // 确保member对象存在且有必要的属性
            if (!member || !member.name || !member.email) return false;
            const matchesSearch = !searchText || member.name.toLowerCase().includes(searchText.toLowerCase()) || member.email.toLowerCase().includes(searchText.toLowerCase());
            const matchesStatus = statusFilter === 'all' || statusFilter === 'active' && member.isActive || statusFilter === 'inactive' && !member.isActive || statusFilter === 'creator' && member.isCreator || statusFilter === 'member' && !member.isCreator;
            return matchesSearch && matchesStatus;
        });
        setFilteredMembers(filtered);
    }, [
        members,
        searchText,
        statusFilter
    ]);
    const fetchMembers = async ()=>{
        try {
            setLoading(true);
            const response = await _services.TeamService.getTeamMembers({
                current: 1,
                pageSize: 1000
            });
            // 确保返回的数据是数组格式
            setMembers((response === null || response === void 0 ? void 0 : response.list) || []);
        } catch (error) {
            console.error('获取团队成员失败:', error);
            _antd.message.error('获取团队成员失败');
            // 出错时设置为空数组
            setMembers([]);
        } finally{
            setLoading(false);
        }
    };
    const handleRemoveMember = (member)=>{
        if (member.isCreator) {
            _antd.message.warning('不能移除团队创建者');
            return;
        }
        _antd.Modal.confirm({
            title: '确认移除成员',
            content: `确定要移除成员 "${member.name}" 吗？`,
            okText: '确认',
            cancelText: '取消',
            onOk: async ()=>{
                try {
                    await _services.TeamService.removeMember(member.id);
                    _antd.message.success('成员移除成功');
                    fetchMembers();
                    onMemberChange === null || onMemberChange === void 0 || onMemberChange();
                } catch (error) {
                    console.error('移除成员失败:', error);
                }
            }
        });
    };
    const handleBatchRemove = ()=>{
        const selectedMembers = members.filter((member)=>selectedRowKeys.includes(member.id) && !member.isCreator);
        if (selectedMembers.length === 0) {
            _antd.message.warning('请选择要移除的成员');
            return;
        }
        _antd.Modal.confirm({
            title: '批量移除成员',
            content: `确定要移除选中的 ${selectedMembers.length} 名成员吗？`,
            okText: '确认',
            cancelText: '取消',
            onOk: async ()=>{
                try {
                    await Promise.all(selectedMembers.map((member)=>_services.TeamService.removeMember(member.id)));
                    _antd.message.success(`成功移除 ${selectedMembers.length} 名成员`);
                    setSelectedRowKeys([]);
                    fetchMembers();
                    onMemberChange === null || onMemberChange === void 0 || onMemberChange();
                } catch (error) {
                    console.error('批量移除成员失败:', error);
                    _antd.message.error('批量移除失败');
                }
            }
        });
    };
    const columns = [
        {
            title: '成员',
            dataIndex: 'name',
            key: 'name',
            render: (name, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                            size: "small",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                lineNumber: 173,
                                columnNumber: 38
                            }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 173,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    children: name
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                    lineNumber: 175,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        fontSize: 12,
                                        color: '#999'
                                    },
                                    children: record.email
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                    lineNumber: 176,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 174,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 172,
                    columnNumber: 9
                }, this)
        },
        {
            title: '角色',
            dataIndex: 'isCreator',
            key: 'role',
            width: 100,
            render: (isCreator)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                    color: isCreator ? 'gold' : 'blue',
                    icon: isCreator ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                        lineNumber: 187,
                        columnNumber: 68
                    }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                        lineNumber: 187,
                        columnNumber: 88
                    }, void 0),
                    children: isCreator ? '创建者' : '成员'
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 187,
                    columnNumber: 9
                }, this)
        },
        {
            title: '状态',
            dataIndex: 'isActive',
            key: 'status',
            width: 80,
            render: (isActive)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                    color: isActive ? 'green' : 'red',
                    children: isActive ? '活跃' : '停用'
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 198,
                    columnNumber: 9
                }, this)
        },
        {
            title: '加入时间',
            dataIndex: 'assignedAt',
            key: 'assignedAt',
            width: 150,
            render: (assignedAt)=>new Date(assignedAt).toLocaleDateString()
        },
        {
            title: '最后访问',
            dataIndex: 'lastAccessTime',
            key: 'lastAccessTime',
            width: 150,
            render: (lastAccessTime)=>{
                const date = new Date(lastAccessTime);
                const now = new Date();
                const diffDays = Math.floor((now.getTime() - date.getTime()) / 86400000);
                let color = 'green';
                if (diffDays > 7) color = 'orange';
                if (diffDays > 30) color = 'red';
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                    title: date.toLocaleString(),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                        color: color,
                        children: diffDays === 0 ? '今天' : `${diffDays}天前`
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                        lineNumber: 226,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 225,
                    columnNumber: 11
                }, this);
            }
        },
        {
            title: '操作',
            key: 'action',
            width: 120,
            render: (_, record)=>{
                if (!isCreator || record.isCreator) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                    type: "secondary",
                    children: "-"
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 239,
                    columnNumber: 18
                }, this);
                const menuItems = [
                    {
                        key: 'remove',
                        label: '移除成员',
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 246,
                            columnNumber: 19
                        }, this),
                        danger: true,
                        onClick: ()=>handleRemoveMember(record)
                    }
                ];
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    size: "small",
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "text",
                            danger: true,
                            size: "small",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                lineNumber: 258,
                                columnNumber: 21
                            }, void 0),
                            onClick: ()=>handleRemoveMember(record),
                            children: "移除"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 254,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                            menu: {
                                items: menuItems
                            },
                            trigger: [
                                'click'
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "text",
                                size: "small",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MoreOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                    lineNumber: 267,
                                    columnNumber: 23
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                lineNumber: 264,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 263,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 253,
                    columnNumber: 11
                }, this);
            }
        }
    ];
    const rowSelection = {
        selectedRowKeys,
        onChange: (newSelectedRowKeys)=>{
            setSelectedRowKeys(newSelectedRowKeys);
        },
        getCheckboxProps: (record)=>({
                disabled: record.isCreator
            })
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                    strong: true,
                    children: "团队成员"
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 290,
                    columnNumber: 11
                }, void 0),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Badge, {
                    count: filteredMembers.length,
                    showZero: true
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 291,
                    columnNumber: 11
                }, void 0)
            ]
        }, void 0, true, {
            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
            lineNumber: 289,
            columnNumber: 9
        }, void 0),
        extra: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                    value: statusFilter,
                    onChange: setStatusFilter,
                    style: {
                        width: 120
                    },
                    size: "small",
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                            value: "all",
                            children: "全部"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 302,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                            value: "active",
                            children: "活跃"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 303,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                            value: "inactive",
                            children: "停用"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 304,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                            value: "creator",
                            children: "创建者"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 305,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                            value: "member",
                            children: "成员"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 306,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 296,
                    columnNumber: 11
                }, void 0),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                    placeholder: "搜索成员",
                    prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                        lineNumber: 310,
                        columnNumber: 21
                    }, void 0),
                    value: searchText,
                    onChange: (e)=>setSearchText(e.target.value),
                    style: {
                        width: 200
                    },
                    size: "small"
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 308,
                    columnNumber: 11
                }, void 0)
            ]
        }, void 0, true, {
            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
            lineNumber: 295,
            columnNumber: 9
        }, void 0),
        children: [
            selectedRowKeys.length > 0 && isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    marginBottom: 16,
                    padding: 12,
                    background: '#f5f5f5',
                    borderRadius: 6
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            children: [
                                "已选择 ",
                                selectedRowKeys.length,
                                " 名成员"
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 322,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            size: "small",
                            danger: true,
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                lineNumber: 326,
                                columnNumber: 21
                            }, void 0),
                            onClick: handleBatchRemove,
                            children: "批量移除"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 323,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            size: "small",
                            onClick: ()=>setSelectedRowKeys([]),
                            children: "取消选择"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 331,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 321,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                lineNumber: 320,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                columns: columns,
                dataSource: filteredMembers,
                rowKey: "id",
                loading: loading,
                rowSelection: isCreator ? rowSelection : undefined,
                pagination: {
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total)=>`共 ${total} 名成员`,
                    pageSize: 10
                }
            }, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                lineNumber: 341,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
        lineNumber: 287,
        columnNumber: 5
    }, this);
};
_s(TeamMemberList, "VFoYmIR+E+CHWgWFfALfNS25o10=");
_c = TeamMemberList;
var _default = TeamMemberList;
var _c;
$RefreshReg$(_c, "TeamMemberList");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/user/components/UserProfileContent.tsx": function (module, exports, __mako_require__){
/**
 * 用户资料内容组件
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
const UserProfileContent = ()=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(true);
    const [saving, setSaving] = (0, _react.useState)(false);
    const [editing, setEditing] = (0, _react.useState)(false);
    const [userProfile, setUserProfile] = (0, _react.useState)(null);
    const [passwordModalVisible, setPasswordModalVisible] = (0, _react.useState)(false);
    const [form] = _antd.Form.useForm();
    const [passwordForm] = _antd.Form.useForm();
    (0, _react.useEffect)(()=>{
        fetchUserProfile();
    }, []);
    const fetchUserProfile = async ()=>{
        try {
            setLoading(true);
            const profile = await _services.UserService.getUserProfile();
            setUserProfile(profile);
            form.setFieldsValue({
                username: profile.username,
                email: profile.email,
                phone: profile.phone,
                realName: profile.realName
            });
        } catch (error) {
            console.error('获取用户资料失败:', error);
            _antd.message.error('获取用户资料失败');
        } finally{
            setLoading(false);
        }
    };
    const handleSaveProfile = async (values)=>{
        try {
            setSaving(true);
            const updateData = {
                email: values.email,
                phone: values.phone,
                realName: values.realName
            };
            const updatedProfile = await _services.UserService.updateUserProfile(updateData);
            setUserProfile(updatedProfile);
            setEditing(false);
            _antd.message.success('个人资料更新成功');
        } catch (error) {
            console.error('更新个人资料失败:', error);
        } finally{
            setSaving(false);
        }
    };
    const handleChangePassword = async (values)=>{
        try {
            await _services.UserService.changePassword({
                oldPassword: values.oldPassword,
                newPassword: values.newPassword
            });
            setPasswordModalVisible(false);
            passwordForm.resetFields();
            _antd.message.success('密码修改成功');
        } catch (error) {
            console.error('修改密码失败:', error);
        }
    };
    const handleCancel = ()=>{
        setEditing(false);
        if (userProfile) form.setFieldsValue({
            username: userProfile.username,
            email: userProfile.email,
            phone: userProfile.phone,
            realName: userProfile.realName
        });
    };
    if (loading || !userProfile) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: "加载中..."
    }, void 0, false, {
        fileName: "src/pages/user/components/UserProfileContent.tsx",
        lineNumber: 110,
        columnNumber: 12
    }, this);
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    display: 'flex',
                    alignItems: 'flex-start',
                    gap: 24
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            textAlign: 'center'
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                size: 120,
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                                    lineNumber: 118,
                                    columnNumber: 36
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/user/components/UserProfileContent.tsx",
                                lineNumber: 118,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    marginTop: 16
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Upload, {
                                    showUploadList: false,
                                    beforeUpload: ()=>{
                                        _antd.message.info('头像上传功能暂未实现');
                                        return false;
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UploadOutlined, {}, void 0, false, {
                                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                                            lineNumber: 127,
                                            columnNumber: 29
                                        }, void 0),
                                        size: "small",
                                        children: "更换头像"
                                    }, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 127,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                                    lineNumber: 120,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/user/components/UserProfileContent.tsx",
                                lineNumber: 119,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                        lineNumber: 117,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            flex: 1
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    marginBottom: 24
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                        level: 4,
                                        style: {
                                            margin: 0
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                fileName: "src/pages/user/components/UserProfileContent.tsx",
                                                lineNumber: 138,
                                                columnNumber: 15
                                            }, this),
                                            " 基本信息"
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 137,
                                        columnNumber: 13
                                    }, this),
                                    !editing && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                                            lineNumber: 143,
                                            columnNumber: 23
                                        }, void 0),
                                        onClick: ()=>setEditing(true),
                                        children: "编辑资料"
                                    }, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 141,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/user/components/UserProfileContent.tsx",
                                lineNumber: 136,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                form: form,
                                layout: "vertical",
                                onFinish: handleSaveProfile,
                                disabled: !editing,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: "用户名",
                                        name: "username",
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                fileName: "src/pages/user/components/UserProfileContent.tsx",
                                                lineNumber: 162,
                                                columnNumber: 25
                                            }, void 0),
                                            disabled: true,
                                            placeholder: "用户名不可修改"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                                            lineNumber: 161,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 157,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: "邮箱地址",
                                        name: "email",
                                        rules: [
                                            {
                                                required: true,
                                                message: '请输入邮箱地址'
                                            },
                                            {
                                                type: 'email',
                                                message: '请输入有效的邮箱地址'
                                            }
                                        ],
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                                fileName: "src/pages/user/components/UserProfileContent.tsx",
                                                lineNumber: 177,
                                                columnNumber: 25
                                            }, void 0),
                                            placeholder: "请输入邮箱地址"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                                            lineNumber: 176,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 168,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: "手机号码",
                                        name: "phone",
                                        rules: [
                                            {
                                                pattern: /^1[3-9]\d{9}$/,
                                                message: '请输入有效的手机号码'
                                            }
                                        ],
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                            placeholder: "请输入手机号码"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                                            lineNumber: 189,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 182,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: "真实姓名",
                                        name: "realName",
                                        rules: [
                                            {
                                                max: 20,
                                                message: '真实姓名不能超过20个字符'
                                            }
                                        ],
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                            placeholder: "请输入真实姓名"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                                            lineNumber: 199,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 192,
                                        columnNumber: 13
                                    }, this),
                                    editing && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "primary",
                                                    htmlType: "submit",
                                                    loading: saving,
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SaveOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                                        lineNumber: 209,
                                                        columnNumber: 27
                                                    }, void 0),
                                                    children: "保存修改"
                                                }, void 0, false, {
                                                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                                                    lineNumber: 205,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    onClick: handleCancel,
                                                    children: "取消"
                                                }, void 0, false, {
                                                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                                                    lineNumber: 213,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                                            lineNumber: 204,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 203,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/user/components/UserProfileContent.tsx",
                                lineNumber: 151,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                        lineNumber: 135,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/user/components/UserProfileContent.tsx",
                lineNumber: 115,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                fileName: "src/pages/user/components/UserProfileContent.tsx",
                lineNumber: 223,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                        level: 4,
                        style: {
                            marginBottom: 16
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.LockOutlined, {}, void 0, false, {
                                fileName: "src/pages/user/components/UserProfileContent.tsx",
                                lineNumber: 228,
                                columnNumber: 11
                            }, this),
                            " 安全设置"
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                        lineNumber: 227,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            padding: '16px 0'
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: "登录密码"
                                    }, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 232,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 233,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "定期更换密码可以提高账户安全性"
                                    }, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 234,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/user/components/UserProfileContent.tsx",
                                lineNumber: 231,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                onClick: ()=>setPasswordModalVisible(true),
                                children: "修改密码"
                            }, void 0, false, {
                                fileName: "src/pages/user/components/UserProfileContent.tsx",
                                lineNumber: 236,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                        lineNumber: 230,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/user/components/UserProfileContent.tsx",
                lineNumber: 226,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "修改密码",
                open: passwordModalVisible,
                onCancel: ()=>{
                    setPasswordModalVisible(false);
                    passwordForm.resetFields();
                },
                footer: null,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: passwordForm,
                    layout: "vertical",
                    onFinish: handleChangePassword,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "当前密码",
                            name: "oldPassword",
                            rules: [
                                {
                                    required: true,
                                    message: '请输入当前密码'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.Password, {
                                placeholder: "请输入当前密码"
                            }, void 0, false, {
                                fileName: "src/pages/user/components/UserProfileContent.tsx",
                                lineNumber: 264,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                            lineNumber: 259,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "新密码",
                            name: "newPassword",
                            rules: [
                                {
                                    required: true,
                                    message: '请输入新密码'
                                },
                                {
                                    min: 6,
                                    message: '密码长度至少6位'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.Password, {
                                placeholder: "请输入新密码"
                            }, void 0, false, {
                                fileName: "src/pages/user/components/UserProfileContent.tsx",
                                lineNumber: 275,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                            lineNumber: 267,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "确认新密码",
                            name: "confirmPassword",
                            dependencies: [
                                'newPassword'
                            ],
                            rules: [
                                {
                                    required: true,
                                    message: '请确认新密码'
                                },
                                ({ getFieldValue })=>({
                                        validator (_, value) {
                                            if (!value || getFieldValue('newPassword') === value) return Promise.resolve();
                                            return Promise.reject(new Error('两次输入的密码不一致'));
                                        }
                                    })
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.Password, {
                                placeholder: "请再次输入新密码"
                            }, void 0, false, {
                                fileName: "src/pages/user/components/UserProfileContent.tsx",
                                lineNumber: 294,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                            lineNumber: 278,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            style: {
                                marginBottom: 0,
                                textAlign: 'right'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: ()=>{
                                            setPasswordModalVisible(false);
                                            passwordForm.resetFields();
                                        },
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 299,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        htmlType: "submit",
                                        children: "确认修改"
                                    }, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 305,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/user/components/UserProfileContent.tsx",
                                lineNumber: 298,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                            lineNumber: 297,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                    lineNumber: 254,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/user/components/UserProfileContent.tsx",
                lineNumber: 245,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/user/components/UserProfileContent.tsx",
        lineNumber: 114,
        columnNumber: 5
    }, this);
};
_s(UserProfileContent, "yzYoMVvE5uRUcouf+5TWbgiw15g=", false, function() {
    return [
        _antd.Form.useForm,
        _antd.Form.useForm
    ];
});
_c = UserProfileContent;
var _default = UserProfileContent;
var _c;
$RefreshReg$(_c, "UserProfileContent");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/user/components/UserSettingsContent.tsx": function (module, exports, __mako_require__){
/**
 * 用户设置内容组件
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
const { Option } = _antd.Select;
const UserSettingsContent = ()=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(true);
    const [saving, setSaving] = (0, _react.useState)(false);
    const [preferences, setPreferences] = (0, _react.useState)(null);
    const [form] = _antd.Form.useForm();
    (0, _react.useEffect)(()=>{
        fetchUserPreferences();
    }, []);
    const fetchUserPreferences = async ()=>{
        try {
            setLoading(true);
            const prefs = await _services.UserService.getUserPreferences();
            setPreferences(prefs);
            form.setFieldsValue({
                language: prefs.language,
                timezone: prefs.timezone,
                theme: prefs.theme,
                emailNotifications: prefs.notifications.email,
                pushNotifications: prefs.notifications.push,
                teamInviteNotifications: prefs.notifications.teamInvites,
                systemUpdateNotifications: prefs.notifications.systemUpdates
            });
        } catch (error) {
            console.error('获取用户偏好设置失败:', error);
            _antd.message.error('获取用户偏好设置失败');
        } finally{
            setLoading(false);
        }
    };
    const handleSaveSettings = async (values)=>{
        try {
            setSaving(true);
            const updateData = {
                language: values.language,
                timezone: values.timezone,
                theme: values.theme,
                notifications: {
                    email: values.emailNotifications,
                    push: values.pushNotifications,
                    teamInvites: values.teamInviteNotifications,
                    systemUpdates: values.systemUpdateNotifications
                }
            };
            await _services.UserService.updateUserPreferences(updateData);
            setPreferences({
                ...preferences,
                ...updateData
            });
            _antd.message.success('设置保存成功');
        } catch (error) {
            console.error('保存设置失败:', error);
        } finally{
            setSaving(false);
        }
    };
    if (loading || !preferences) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: "加载中..."
    }, void 0, false, {
        fileName: "src/pages/user/components/UserSettingsContent.tsx",
        lineNumber: 100,
        columnNumber: 12
    }, this);
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
        form: form,
        layout: "vertical",
        onFinish: handleSaveSettings,
        initialValues: {
            language: preferences.language,
            timezone: preferences.timezone,
            theme: preferences.theme,
            emailNotifications: preferences.notifications.email,
            pushNotifications: preferences.notifications.push,
            teamInviteNotifications: preferences.notifications.teamInvites,
            systemUpdateNotifications: preferences.notifications.systemUpdates
        },
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.GlobalOutlined, {}, void 0, false, {
                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                            lineNumber: 119,
                            columnNumber: 22
                        }, void 0),
                        " 基础设置"
                    ]
                }, void 0, true),
                style: {
                    marginBottom: 24
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                        label: "界面语言",
                        name: "language",
                        tooltip: "选择您偏好的界面显示语言",
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                            placeholder: "请选择语言",
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                    value: "zh-CN",
                                    children: "简体中文"
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                    lineNumber: 126,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                    value: "en-US",
                                    children: "English"
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                    lineNumber: 127,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                    value: "ja-JP",
                                    children: "日本語"
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                    lineNumber: 128,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                            lineNumber: 125,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/user/components/UserSettingsContent.tsx",
                        lineNumber: 120,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                        label: "时区设置",
                        name: "timezone",
                        tooltip: "选择您所在的时区，影响时间显示",
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                            placeholder: "请选择时区",
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                    value: "Asia/Shanghai",
                                    children: "中国标准时间 (UTC+8)"
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                    lineNumber: 138,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                    value: "America/New_York",
                                    children: "美国东部时间 (UTC-5)"
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                    lineNumber: 139,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                    value: "Europe/London",
                                    children: "英国时间 (UTC+0)"
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                    lineNumber: 140,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                    value: "Asia/Tokyo",
                                    children: "日本标准时间 (UTC+9)"
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                    lineNumber: 141,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                            lineNumber: 137,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/user/components/UserSettingsContent.tsx",
                        lineNumber: 132,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/user/components/UserSettingsContent.tsx",
                lineNumber: 119,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EyeOutlined, {}, void 0, false, {
                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                            lineNumber: 147,
                            columnNumber: 22
                        }, void 0),
                        " 外观设置"
                    ]
                }, void 0, true),
                style: {
                    marginBottom: 24
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                        label: "主题模式",
                        name: "theme",
                        tooltip: "选择您偏好的界面主题",
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Radio.Group, {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Radio, {
                                    value: "light",
                                    children: "浅色模式"
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                    lineNumber: 154,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Radio, {
                                    value: "dark",
                                    children: "深色模式"
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                    lineNumber: 155,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Radio, {
                                    value: "auto",
                                    children: "跟随系统"
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                    lineNumber: 156,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                            lineNumber: 153,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/user/components/UserSettingsContent.tsx",
                        lineNumber: 148,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                        message: "主题设置",
                        description: "深色模式可以减少眼部疲劳，特别适合在光线较暗的环境中使用。",
                        type: "info",
                        showIcon: true,
                        style: {
                            marginTop: 16
                        }
                    }, void 0, false, {
                        fileName: "src/pages/user/components/UserSettingsContent.tsx",
                        lineNumber: 160,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/user/components/UserSettingsContent.tsx",
                lineNumber: 147,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.BellOutlined, {}, void 0, false, {
                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                            lineNumber: 170,
                            columnNumber: 22
                        }, void 0),
                        " 通知设置"
                    ]
                }, void 0, true),
                style: {
                    marginBottom: 24
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    direction: "vertical",
                    style: {
                        width: '100%'
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center'
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: "邮件通知"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                            lineNumber: 174,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                            lineNumber: 175,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            type: "secondary",
                                            children: "接收重要事件的邮件通知"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                            lineNumber: 176,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                    lineNumber: 173,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    name: "emailNotifications",
                                    valuePropName: "checked",
                                    style: {
                                        margin: 0
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {}, void 0, false, {
                                        fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                        lineNumber: 179,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                    lineNumber: 178,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                            lineNumber: 172,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                            style: {
                                margin: '12px 0'
                            }
                        }, void 0, false, {
                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                            lineNumber: 183,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center'
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: "浏览器推送通知"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                            lineNumber: 187,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                            lineNumber: 188,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            type: "secondary",
                                            children: "在浏览器中接收实时通知"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                            lineNumber: 189,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                    lineNumber: 186,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    name: "pushNotifications",
                                    valuePropName: "checked",
                                    style: {
                                        margin: 0
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {}, void 0, false, {
                                        fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                        lineNumber: 192,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                    lineNumber: 191,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                            lineNumber: 185,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                            style: {
                                margin: '12px 0'
                            }
                        }, void 0, false, {
                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                            lineNumber: 196,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center'
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: "团队邀请通知"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                            lineNumber: 200,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                            lineNumber: 201,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            type: "secondary",
                                            children: "当有人邀请您加入团队时通知"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                            lineNumber: 202,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                    lineNumber: 199,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    name: "teamInviteNotifications",
                                    valuePropName: "checked",
                                    style: {
                                        margin: 0
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {}, void 0, false, {
                                        fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                        lineNumber: 205,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                    lineNumber: 204,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                            lineNumber: 198,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                            style: {
                                margin: '12px 0'
                            }
                        }, void 0, false, {
                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                            lineNumber: 209,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center'
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: "系统更新通知"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                            lineNumber: 213,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                            lineNumber: 214,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            type: "secondary",
                                            children: "接收系统功能更新和维护通知"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                            lineNumber: 215,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                    lineNumber: 212,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    name: "systemUpdateNotifications",
                                    valuePropName: "checked",
                                    style: {
                                        margin: 0
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {}, void 0, false, {
                                        fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                        lineNumber: 218,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                    lineNumber: 217,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/components/UserSettingsContent.tsx",
                            lineNumber: 211,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/user/components/UserSettingsContent.tsx",
                    lineNumber: 171,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/user/components/UserSettingsContent.tsx",
                lineNumber: 170,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "primary",
                                htmlType: "submit",
                                loading: saving,
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SaveOutlined, {}, void 0, false, {
                                    fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                    lineNumber: 232,
                                    columnNumber: 21
                                }, void 0),
                                size: "large",
                                children: "保存设置"
                            }, void 0, false, {
                                fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                lineNumber: 228,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                onClick: ()=>{
                                    form.resetFields();
                                    _antd.message.info('已重置为上次保存的设置');
                                },
                                children: "重置"
                            }, void 0, false, {
                                fileName: "src/pages/user/components/UserSettingsContent.tsx",
                                lineNumber: 237,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/user/components/UserSettingsContent.tsx",
                        lineNumber: 227,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/user/components/UserSettingsContent.tsx",
                    lineNumber: 226,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/user/components/UserSettingsContent.tsx",
                lineNumber: 225,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/user/components/UserSettingsContent.tsx",
        lineNumber: 104,
        columnNumber: 5
    }, this);
};
_s(UserSettingsContent, "4kypMJNkfJH+i0nKfpdmf0L1h9U=", false, function() {
    return [
        _antd.Form.useForm
    ];
});
_c = UserSettingsContent;
var _default = UserSettingsContent;
var _c;
$RefreshReg$(_c, "UserSettingsContent");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=common-async.js.map