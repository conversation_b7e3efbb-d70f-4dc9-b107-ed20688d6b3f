globalThis.makoModuleHotUpdate('common', {
    modules: {
        "src/pages/team/components/TeamListContent.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _services = __mako_require__("src/services/index.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            const { Search } = _antd.Input;
            const TeamListContent = ()=>{
                _s();
                const [loading, setLoading] = (0, _react.useState)(true);
                const [teams, setTeams] = (0, _react.useState)([]);
                const [filteredTeams, setFilteredTeams] = (0, _react.useState)([]);
                const [searchText, setSearchText] = (0, _react.useState)('');
                (0, _react.useEffect)(()=>{
                    fetchTeams();
                }, []);
                (0, _react.useEffect)(()=>{
                    // 过滤团队列表
                    const filtered = teams.filter((team)=>team.name.toLowerCase().includes(searchText.toLowerCase()) || team.description && team.description.toLowerCase().includes(searchText.toLowerCase()));
                    setFilteredTeams(filtered);
                }, [
                    teams,
                    searchText
                ]);
                const fetchTeams = async ()=>{
                    try {
                        setLoading(true);
                        const teamList = await _services.TeamService.getUserTeams();
                        setTeams(teamList);
                    } catch (error) {
                        console.error('获取团队列表失败:', error);
                        _antd.message.error('获取团队列表失败');
                    } finally{
                        setLoading(false);
                    }
                };
                const handleCreateTeam = ()=>{
                    _max.history.push('/team/create');
                };
                const handleViewTeam = async (team)=>{
                    try {
                        // 切换到该团队
                        const response = await _services.AuthService.teamLogin({
                            teamId: team.id
                        });
                        // 检查后端返回的团队选择成功标识
                        if (response.teamSelectionSuccess && response.team && response.team.id === team.id) _antd.message.success(`已切换到团队：${team.name}`);
                        else {
                            console.error('团队切换响应异常，未返回正确的团队信息');
                            _antd.message.error('团队切换失败，请重试');
                        }
                    } catch (error) {
                        console.error('切换团队失败:', error);
                        _antd.message.error('切换团队失败');
                    }
                };
                const handleSwitchTeam = async (team)=>{
                    try {
                        const response = await _services.AuthService.teamLogin({
                            teamId: team.id
                        });
                        // 检查后端返回的团队选择成功标识
                        if (response.teamSelectionSuccess && response.team && response.team.id === team.id) {
                            _antd.message.success(`已切换到团队：${team.name}`);
                            // 等待一段时间确保 Token 更新完成后再跳转
                            setTimeout(()=>{
                                _max.history.push('/');
                            }, 200);
                        } else {
                            console.error('团队切换响应异常，未返回正确的团队信息');
                            _antd.message.error('团队切换失败，请重试');
                        }
                    } catch (error) {
                        console.error('切换团队失败:', error);
                        _antd.message.error('切换团队失败');
                    }
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginBottom: 16
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Search, {
                                placeholder: "搜索团队名称或描述",
                                allowClear: true,
                                value: searchText,
                                onChange: (e)=>setSearchText(e.target.value),
                                style: {
                                    width: 300
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                lineNumber: 111,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/components/TeamListContent.tsx",
                            lineNumber: 110,
                            columnNumber: 7
                        }, this),
                        filteredTeams.length === 0 && !loading ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
                            image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
                            description: searchText ? '没有找到匹配的团队' : '您还没有加入任何团队',
                            children: !searchText && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "primary",
                                onClick: handleCreateTeam,
                                children: "创建第一个团队"
                            }, void 0, false, {
                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                lineNumber: 128,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/components/TeamListContent.tsx",
                            lineNumber: 121,
                            columnNumber: 9
                        }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                            loading: loading,
                            itemLayout: "horizontal",
                            dataSource: filteredTeams,
                            pagination: {
                                showSizeChanger: true,
                                showQuickJumper: true,
                                showTotal: (total)=>`共 ${total} 个团队`
                            },
                            renderItem: (team)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                    actions: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "text",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EyeOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                                lineNumber: 149,
                                                columnNumber: 25
                                            }, void 0),
                                            onClick: ()=>handleViewTeam(team),
                                            children: "查看详情"
                                        }, "view", false, {
                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                            lineNumber: 146,
                                            columnNumber: 17
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "primary",
                                            size: "small",
                                            onClick: ()=>handleSwitchTeam(team),
                                            children: "进入团队"
                                        }, "switch", false, {
                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                            lineNumber: 154,
                                            columnNumber: 17
                                        }, void 0)
                                    ],
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item.Meta, {
                                        avatar: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                            size: 64,
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                                lineNumber: 168,
                                                columnNumber: 27
                                            }, void 0),
                                            style: {
                                                backgroundColor: '#1890ff'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                            lineNumber: 166,
                                            columnNumber: 19
                                        }, void 0),
                                        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                    level: 4,
                                                    style: {
                                                        margin: 0
                                                    },
                                                    children: team.name
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                    lineNumber: 174,
                                                    columnNumber: 21
                                                }, void 0),
                                                team.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                    color: "gold",
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team/components/TeamListContent.tsx",
                                                        lineNumber: 178,
                                                        columnNumber: 47
                                                    }, void 0),
                                                    children: "创建者"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                    lineNumber: 178,
                                                    columnNumber: 23
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                            lineNumber: 173,
                                            columnNumber: 19
                                        }, void 0),
                                        description: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            direction: "vertical",
                                            size: "small",
                                            children: [
                                                team.description && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    type: "secondary",
                                                    children: team.description
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                    lineNumber: 187,
                                                    columnNumber: 23
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                            size: "small",
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                                    lineNumber: 191,
                                                                    columnNumber: 25
                                                                }, void 0),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    type: "secondary",
                                                                    children: [
                                                                        team.memberCount,
                                                                        " 名成员"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                                    lineNumber: 192,
                                                                    columnNumber: 25
                                                                }, void 0)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                                            lineNumber: 190,
                                                            columnNumber: 23
                                                        }, void 0),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            type: "secondary",
                                                            children: [
                                                                "创建于 ",
                                                                new Date(team.createdAt).toLocaleDateString()
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                                            lineNumber: 194,
                                                            columnNumber: 23
                                                        }, void 0),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            type: "secondary",
                                                            children: [
                                                                "更新于 ",
                                                                new Date(team.updatedAt).toLocaleDateString()
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                                            lineNumber: 197,
                                                            columnNumber: 23
                                                        }, void 0)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                    lineNumber: 189,
                                                    columnNumber: 21
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                            lineNumber: 185,
                                            columnNumber: 19
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/components/TeamListContent.tsx",
                                        lineNumber: 164,
                                        columnNumber: 15
                                    }, void 0)
                                }, void 0, false, {
                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                    lineNumber: 144,
                                    columnNumber: 13
                                }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/team/components/TeamListContent.tsx",
                            lineNumber: 134,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/components/TeamListContent.tsx",
                    lineNumber: 109,
                    columnNumber: 5
                }, this);
            };
            _s(TeamListContent, "6oD2tIqxbPJXnbsp9fetgXShQMo=");
            _c = TeamListContent;
            var _default = TeamListContent;
            var _c;
            $RefreshReg$(_c, "TeamListContent");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '3207233597650937199';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "common",
            "p__subscription__index"
        ],
        "src/pages/team/create/index.tsx": [
            "p__team__create__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "common",
            "p__user__index"
        ],
        "src/pages/user/login/index.tsx": [
            "p__user__login__index"
        ],
        "src/pages/user/team-select/index.tsx": [
            "src/pages/user/team-select/index.tsx"
        ]
    });
    ;
});

//# sourceMappingURL=common-async.14882008901790612903.hot-update.js.map