{"version": 3, "sources": ["src/pages/subscription/components/SubscriptionManageContent.tsx", "src/pages/subscription/components/SubscriptionPlansContent.tsx", "src/pages/team/components/TeamListContent.tsx", "src/pages/team/detail/components/InviteMemberModal.tsx", "src/pages/team/detail/components/MemberAssignModal.tsx", "src/pages/team/detail/components/TeamDetailContent.tsx", "src/pages/team/detail/components/TeamMemberList.tsx", "src/pages/user/components/UserProfileContent.tsx", "src/pages/user/components/UserSettingsContent.tsx"], "sourcesContent": ["/**\n * 订阅管理内容组件\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  Descriptions, \n  Button, \n  Space, \n  Typography, \n  Tag, \n  Progress,\n  message,\n  Modal,\n  Table,\n  Alert,\n  Empty\n} from 'antd';\nimport {\n  CrownOutlined,\n  UpOutlined,\n  ReloadOutlined,\n  StopOutlined,\n  HistoryOutlined\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport { SubscriptionService } from '@/services';\nimport type { SubscriptionResponse } from '@/types/api';\nimport { SubscriptionStatus } from '@/types/api';\n\nconst { Title, Text } = Typography;\n\ninterface SubscriptionManageContentProps {\n  currentSubscription: SubscriptionResponse | null;\n  loading: boolean;\n  onRefresh: () => void;\n}\n\nconst SubscriptionManageContent: React.FC<SubscriptionManageContentProps> = ({\n  currentSubscription,\n  loading,\n  onRefresh\n}) => {\n  const [subscriptionHistory, setSubscriptionHistory] = useState<SubscriptionResponse[]>([]);\n  const [usageInfo, setUsageInfo] = useState<any>(null);\n  const [historyModalVisible, setHistoryModalVisible] = useState(false);\n\n  useEffect(() => {\n    if (currentSubscription) {\n      fetchSubscriptionHistory();\n      fetchUsageInfo();\n    }\n  }, [currentSubscription]);\n\n  const fetchSubscriptionHistory = async () => {\n    try {\n      const history = await SubscriptionService.getSubscriptionHistory();\n      setSubscriptionHistory(history);\n    } catch (error) {\n      console.error('获取订阅历史失败:', error);\n    }\n  };\n\n  const fetchUsageInfo = async () => {\n    try {\n      const usage = await SubscriptionService.getUsageInfo();\n      setUsageInfo(usage);\n    } catch (error) {\n      console.error('获取使用情况失败:', error);\n    }\n  };\n\n  const handleRenew = async () => {\n    if (!currentSubscription) return;\n    \n    try {\n      await SubscriptionService.renewSubscription(currentSubscription.id);\n      message.success('续费成功！');\n      onRefresh();\n    } catch (error) {\n      console.error('续费失败:', error);\n    }\n  };\n\n  const handleCancel = async () => {\n    if (!currentSubscription) return;\n    \n    Modal.confirm({\n      title: '确认取消订阅',\n      content: '取消订阅后，您将失去当前套餐的所有权益。确定要取消吗？',\n      okText: '确认取消',\n      cancelText: '我再想想',\n      okType: 'danger',\n      onOk: async () => {\n        try {\n          await SubscriptionService.cancelSubscription(currentSubscription.id);\n          message.success('订阅已取消');\n          onRefresh();\n        } catch (error) {\n          console.error('取消订阅失败:', error);\n        }\n      },\n    });\n  };\n\n  const getStatusTag = (status: SubscriptionStatus) => {\n    const statusMap = {\n      [SubscriptionStatus.ACTIVE]: { color: 'green', text: '有效' },\n      [SubscriptionStatus.EXPIRED]: { color: 'red', text: '已过期' },\n      [SubscriptionStatus.CANCELLED]: { color: 'default', text: '已取消' },\n      [SubscriptionStatus.PENDING]: { color: 'orange', text: '待激活' },\n    };\n    \n    const config = statusMap[status] || { color: 'default', text: '未知' };\n    return <Tag color={config.color}>{config.text}</Tag>;\n  };\n\n  const historyColumns: ColumnsType<SubscriptionResponse> = [\n    {\n      title: '套餐名称',\n      dataIndex: ['plan', 'name'],\n      key: 'planName',\n    },\n    {\n      title: '价格',\n      dataIndex: 'totalPrice',\n      key: 'totalPrice',\n      render: (price: number) => `¥${price}`,\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status: SubscriptionStatus) => getStatusTag(status),\n    },\n    {\n      title: '开始时间',\n      dataIndex: 'startDate',\n      key: 'startDate',\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '结束时间',\n      dataIndex: 'endDate',\n      key: 'endDate',\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n  ];\n\n  if (loading) {\n    return <div>加载中...</div>;\n  }\n\n  if (!currentSubscription) {\n    return (\n      <Empty\n        image={Empty.PRESENTED_IMAGE_SIMPLE}\n        description=\"您还没有任何订阅\"\n      >\n        <Text type=\"secondary\">\n          请前往\"套餐选择\"页面选择适合您的订阅套餐\n        </Text>\n      </Empty>\n    );\n  }\n\n  return (\n    <div>\n      {/* 当前订阅信息 */}\n      <div style={{ marginBottom: 24 }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>\n          <Title level={4} style={{ margin: 0 }}>\n            <CrownOutlined /> 当前订阅\n          </Title>\n          <Space>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={onRefresh}\n            >\n              刷新\n            </Button>\n            <Button\n              icon={<HistoryOutlined />}\n              onClick={() => setHistoryModalVisible(true)}\n            >\n              订阅历史\n            </Button>\n          </Space>\n        </div>\n\n        <Descriptions column={2} bordered>\n          <Descriptions.Item label=\"套餐名称\">\n            {currentSubscription.plan.name}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"订阅状态\">\n            {getStatusTag(currentSubscription.status)}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"开始时间\">\n            {new Date(currentSubscription.startDate).toLocaleDateString()}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"结束时间\">\n            {new Date(currentSubscription.endDate).toLocaleDateString()}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"总价格\">\n            ¥{currentSubscription.totalPrice}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"存储上限\">\n            {currentSubscription.plan.maxSize}GB\n          </Descriptions.Item>\n        </Descriptions>\n\n        {/* 操作按钮 */}\n        <div style={{ marginTop: 16 }}>\n          <Space>\n            {currentSubscription.status === SubscriptionStatus.ACTIVE && (\n              <>\n                <Button\n                  type=\"primary\"\n                  icon={<UpOutlined />}\n                  onClick={handleRenew}\n                >\n                  续费\n                </Button>\n                <Button\n                  danger\n                  icon={<StopOutlined />}\n                  onClick={handleCancel}\n                >\n                  取消订阅\n                </Button>\n              </>\n            )}\n            {currentSubscription.status === SubscriptionStatus.EXPIRED && (\n              <Button\n                type=\"primary\"\n                icon={<ReloadOutlined />}\n                onClick={handleRenew}\n              >\n                重新订阅\n              </Button>\n            )}\n          </Space>\n        </div>\n      </div>\n\n      {/* 使用情况 */}\n      {usageInfo && (\n        <div style={{ marginBottom: 24 }}>\n          <Title level={4}>使用情况</Title>\n          <div style={{ marginBottom: 16 }}>\n            <Text>存储使用量</Text>\n            <Progress\n              percent={Math.round((usageInfo.usedStorage / usageInfo.totalStorage) * 100)}\n              format={() => `${usageInfo.usedStorage}GB / ${usageInfo.totalStorage}GB`}\n              style={{ marginTop: 8 }}\n            />\n          </div>\n          \n          {usageInfo.usedStorage / usageInfo.totalStorage > 0.8 && (\n            <Alert\n              message=\"存储空间不足\"\n              description=\"您的存储空间使用量已超过80%，建议及时清理或升级套餐。\"\n              type=\"warning\"\n              showIcon\n              style={{ marginTop: 16 }}\n            />\n          )}\n        </div>\n      )}\n\n      {/* 订阅历史模态框 */}\n      <Modal\n        title=\"订阅历史\"\n        open={historyModalVisible}\n        onCancel={() => setHistoryModalVisible(false)}\n        footer={null}\n        width={800}\n      >\n        <Table\n          columns={historyColumns}\n          dataSource={subscriptionHistory}\n          rowKey=\"id\"\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 条记录`,\n          }}\n        />\n      </Modal>\n    </div>\n  );\n};\n\nexport default SubscriptionManageContent;\n", "/**\n * 订阅套餐内容组件\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  Row, \n  Col, \n  Button, \n  Typography, \n  Tag, \n  List,\n  message,\n  Modal,\n  InputNumber,\n  Divider,\n  Card\n} from 'antd';\nimport { \n  CrownOutlined, \n  CheckOutlined, \n  StarOutlined,\n  ShoppingCartOutlined\n} from '@ant-design/icons';\nimport { SubscriptionService } from '@/services';\nimport type { SubscriptionPlanResponse, CreateSubscriptionRequest } from '@/types/api';\n\nconst { Title, Text } = Typography;\n\ninterface SubscriptionPlansContentProps {\n  onSubscriptionSuccess?: () => void;\n}\n\nconst SubscriptionPlansContent: React.FC<SubscriptionPlansContentProps> = ({\n  onSubscriptionSuccess\n}) => {\n  const [loading, setLoading] = useState(true);\n  const [plans, setPlans] = useState<SubscriptionPlanResponse[]>([]);\n  const [subscribing, setSubscribing] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlanResponse | null>(null);\n  const [subscribeModalVisible, setSubscribeModalVisible] = useState(false);\n  const [duration, setDuration] = useState(1);\n\n  useEffect(() => {\n    fetchPlans();\n  }, []);\n\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      const planList = await SubscriptionService.getActivePlans();\n      setPlans(planList);\n    } catch (error) {\n      console.error('获取订阅套餐失败:', error);\n      message.error('获取订阅套餐失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSubscribe = (plan: SubscriptionPlanResponse) => {\n    setSelectedPlan(plan);\n    setDuration(1);\n    setSubscribeModalVisible(true);\n  };\n\n  const handleConfirmSubscribe = async () => {\n    if (!selectedPlan) return;\n\n    try {\n      setSubscribing(true);\n      const request: CreateSubscriptionRequest = {\n        planId: selectedPlan.id,\n        duration,\n      };\n      \n      await SubscriptionService.createSubscription(request);\n      setSubscribeModalVisible(false);\n      message.success('订阅成功！');\n      onSubscriptionSuccess?.();\n    } catch (error) {\n      console.error('订阅失败:', error);\n    } finally {\n      setSubscribing(false);\n    }\n  };\n\n  const getPlanFeatures = (plan: SubscriptionPlanResponse) => {\n    const features = [\n      `数据存储上限：${plan.maxSize}GB`,\n      '7x24小时技术支持',\n      '数据备份与恢复',\n      '团队协作功能',\n    ];\n\n    if (plan.price > 0) {\n      features.push('高级分析报告');\n      features.push('API 访问权限');\n    }\n\n    if (plan.price >= 100) {\n      features.push('专属客户经理');\n      features.push('定制化功能');\n      features.push('优先技术支持');\n    }\n\n    return features;\n  };\n\n  const getPlanColor = (plan: SubscriptionPlanResponse) => {\n    if (plan.price === 0) return '#52c41a'; // 免费版 - 绿色\n    if (plan.price < 100) return '#1890ff'; // 基础版 - 蓝色\n    return '#722ed1'; // 专业版 - 紫色\n  };\n\n  const getPlanIcon = (plan: SubscriptionPlanResponse) => {\n    if (plan.price === 0) return <CheckOutlined />;\n    if (plan.price < 100) return <StarOutlined />;\n    return <CrownOutlined />;\n  };\n\n  const calculatePrice = (plan: SubscriptionPlanResponse, months: number) => {\n    return SubscriptionService.calculatePlanPrice(plan, months);\n  };\n\n  return (\n    <div>\n      <Row gutter={[24, 24]}>\n        {plans.map((plan) => {\n          const features = getPlanFeatures(plan);\n          const color = getPlanColor(plan);\n          const icon = getPlanIcon(plan);\n          const isPopular = plan.price > 0 && plan.price < 100;\n\n          return (\n            <Col xs={24} sm={12} lg={8} key={plan.id}>\n              <Card\n                hoverable\n                loading={loading}\n                style={{ \n                  height: '100%',\n                  borderColor: isPopular ? '#1890ff' : undefined,\n                  position: 'relative'\n                }}\n              >\n                {isPopular && (\n                  <div\n                    style={{\n                      position: 'absolute',\n                      top: -1,\n                      right: 24,\n                      background: '#1890ff',\n                      color: 'white',\n                      padding: '4px 12px',\n                      borderRadius: '0 0 8px 8px',\n                      fontSize: 12,\n                      fontWeight: 'bold',\n                    }}\n                  >\n                    推荐\n                  </div>\n                )}\n\n                <div style={{ textAlign: 'center', marginBottom: 24 }}>\n                  <div style={{ fontSize: 48, color, marginBottom: 16 }}>\n                    {icon}\n                  </div>\n                  <Title level={3} style={{ margin: 0, color }}>\n                    {plan.name}\n                  </Title>\n                  <Text type=\"secondary\">{plan.description}</Text>\n                </div>\n\n                <div style={{ textAlign: 'center', marginBottom: 24 }}>\n                  <div style={{ fontSize: 36, fontWeight: 'bold', color }}>\n                    ¥{plan.price}\n                    <span style={{ fontSize: 16, fontWeight: 'normal' }}>/月</span>\n                  </div>\n                  {plan.price === 0 && (\n                    <Tag color=\"green\" style={{ marginTop: 8 }}>\n                      永久免费\n                    </Tag>\n                  )}\n                </div>\n\n                <List\n                  size=\"small\"\n                  dataSource={features}\n                  renderItem={(feature) => (\n                    <List.Item>\n                      <CheckOutlined style={{ color: '#52c41a', marginRight: 8 }} />\n                      {feature}\n                    </List.Item>\n                  )}\n                  style={{ marginBottom: 24 }}\n                />\n\n                <Button\n                  type={isPopular ? 'primary' : 'default'}\n                  size=\"large\"\n                  block\n                  icon={<ShoppingCartOutlined />}\n                  onClick={() => handleSubscribe(plan)}\n                  disabled={plan.price === 0} // 免费套餐不需要订阅\n                >\n                  {plan.price === 0 ? '当前套餐' : '立即订阅'}\n                </Button>\n              </Card>\n            </Col>\n          );\n        })}\n      </Row>\n\n      {/* 套餐对比 */}\n      <Card title=\"套餐对比\" style={{ marginTop: 32 }}>\n        <div style={{ overflowX: 'auto' }}>\n          {plans.length > 0 && (\n            <div>\n              {SubscriptionService.comparePlans(plans).map((comparison, index) => (\n                <Row key={`comparison-${index}`} style={{ padding: '12px 0', borderBottom: '1px solid #f0f0f0' }}>\n                  <Col span={6}>\n                    <Text strong>{comparison.feature}</Text>\n                  </Col>\n                  {comparison.values.map((value, valueIndex) => (\n                    <Col span={6} key={`value-${index}-${valueIndex}`}>\n                      <Text>\n                        {typeof value === 'boolean' ? (value ? '✓' : '✗') : value}\n                      </Text>\n                    </Col>\n                  ))}\n                </Row>\n              ))}\n            </div>\n          )}\n        </div>\n      </Card>\n\n      {/* 订阅确认模态框 */}\n      <Modal\n        title=\"确认订阅\"\n        open={subscribeModalVisible}\n        onCancel={() => setSubscribeModalVisible(false)}\n        footer={[\n          <Button key=\"cancel\" onClick={() => setSubscribeModalVisible(false)}>\n            取消\n          </Button>,\n          <Button\n            key=\"confirm\"\n            type=\"primary\"\n            loading={subscribing}\n            onClick={handleConfirmSubscribe}\n          >\n            确认订阅\n          </Button>,\n        ]}\n      >\n        {selectedPlan && (\n          <div>\n            <div style={{ marginBottom: 16 }}>\n              <Text strong>套餐：</Text>\n              <Text>{selectedPlan.name}</Text>\n            </div>\n            \n            <div style={{ marginBottom: 16 }}>\n              <Text strong>订阅时长：</Text>\n              <InputNumber\n                min={1}\n                max={24}\n                value={duration}\n                onChange={(value) => setDuration(value || 1)}\n                addonAfter=\"个月\"\n                style={{ marginLeft: 8 }}\n              />\n            </div>\n\n            <Divider />\n\n            <div>\n              <Text strong>价格详情：</Text>\n              {(() => {\n                const priceInfo = calculatePrice(selectedPlan, duration);\n                return (\n                  <div style={{ marginTop: 8 }}>\n                    <div>原价：¥{priceInfo.originalPrice}</div>\n                    {priceInfo.discount > 0 && (\n                      <div style={{ color: '#ff4d4f' }}>\n                        折扣：-{priceInfo.discount}%\n                      </div>\n                    )}\n                    <div style={{ fontSize: 18, fontWeight: 'bold', color: '#1890ff' }}>\n                      总计：¥{priceInfo.totalPrice}\n                    </div>\n                  </div>\n                );\n              })()}\n            </div>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default SubscriptionPlansContent;\n", "/**\n * 团队列表内容组件\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  List, \n  Avatar, \n  Button, \n  Space, \n  Typography, \n  Tag, \n  Input,\n  message,\n  Empty\n} from 'antd';\nimport { \n  TeamOutlined, \n  UserOutlined, \n  SearchOutlined,\n  CrownOutlined,\n  EyeOutlined\n} from '@ant-design/icons';\nimport { history } from '@umijs/max';\nimport { TeamService, AuthService } from '@/services';\nimport type { TeamDetailResponse } from '@/types/api';\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\n\nconst TeamListContent: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);\n  const [filteredTeams, setFilteredTeams] = useState<TeamDetailResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n\n  useEffect(() => {\n    fetchTeams();\n  }, []);\n\n  useEffect(() => {\n    // 过滤团队列表\n    const filtered = teams.filter(team =>\n      team.name.toLowerCase().includes(searchText.toLowerCase()) ||\n      (team.description && team.description.toLowerCase().includes(searchText.toLowerCase()))\n    );\n    setFilteredTeams(filtered);\n  }, [teams, searchText]);\n\n  const fetchTeams = async () => {\n    try {\n      setLoading(true);\n      const teamList = await TeamService.getUserTeams();\n      setTeams(teamList);\n    } catch (error) {\n      console.error('获取团队列表失败:', error);\n      message.error('获取团队列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateTeam = () => {\n    history.push('/team/create');\n  };\n\n  const handleViewTeam = async (team: TeamDetailResponse) => {\n    try {\n      // 切换到该团队\n      const response = await AuthService.teamLogin({ teamId: team.id });\n\n      // 检查后端返回的团队选择成功标识\n      if (response.teamSelectionSuccess && response.team && response.team.id === team.id) {\n        message.success(`已切换到团队：${team.name}`);\n        // 不需要刷新页面，让应用自然响应状态变化\n      } else {\n        console.error('团队切换响应异常，未返回正确的团队信息');\n        message.error('团队切换失败，请重试');\n      }\n    } catch (error) {\n      console.error('切换团队失败:', error);\n      message.error('切换团队失败');\n    }\n  };\n\n  const handleSwitchTeam = async (team: TeamDetailResponse) => {\n    try {\n      const response = await AuthService.teamLogin({ teamId: team.id });\n\n      // 检查后端返回的团队选择成功标识\n      if (response.teamSelectionSuccess && response.team && response.team.id === team.id) {\n        message.success(`已切换到团队：${team.name}`);\n\n        // 等待一段时间确保 Token 更新完成后再跳转\n        setTimeout(() => {\n          history.push('/');\n        }, 200);\n      } else {\n        console.error('团队切换响应异常，未返回正确的团队信息');\n        message.error('团队切换失败，请重试');\n      }\n    } catch (error) {\n      console.error('切换团队失败:', error);\n      message.error('切换团队失败');\n    }\n  };\n\n  return (\n    <div>\n      <div style={{ marginBottom: 16 }}>\n        <Search\n          placeholder=\"搜索团队名称或描述\"\n          allowClear\n          value={searchText}\n          onChange={(e) => setSearchText(e.target.value)}\n          style={{ width: 300 }}\n        />\n      </div>\n\n      {filteredTeams.length === 0 && !loading ? (\n        <Empty\n          image={Empty.PRESENTED_IMAGE_SIMPLE}\n          description={\n            searchText ? '没有找到匹配的团队' : '您还没有加入任何团队'\n          }\n        >\n          {!searchText && (\n            <Button type=\"primary\" onClick={handleCreateTeam}>\n              创建第一个团队\n            </Button>\n          )}\n        </Empty>\n      ) : (\n        <List\n          loading={loading}\n          itemLayout=\"horizontal\"\n          dataSource={filteredTeams}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个团队`,\n          }}\n          renderItem={(team) => (\n            <List.Item\n              actions={[\n                <Button\n                  key=\"view\"\n                  type=\"text\"\n                  icon={<EyeOutlined />}\n                  onClick={() => handleViewTeam(team)}\n                >\n                  查看详情\n                </Button>,\n                <Button\n                  key=\"switch\"\n                  type=\"primary\"\n                  size=\"small\"\n                  onClick={() => handleSwitchTeam(team)}\n                >\n                  进入团队\n                </Button>,\n              ]}\n            >\n              <List.Item.Meta\n                avatar={\n                  <Avatar \n                    size={64} \n                    icon={<TeamOutlined />}\n                    style={{ backgroundColor: '#1890ff' }}\n                  />\n                }\n                title={\n                  <Space>\n                    <Title level={4} style={{ margin: 0 }}>\n                      {team.name}\n                    </Title>\n                    {team.isCreator && (\n                      <Tag color=\"gold\" icon={<CrownOutlined />}>\n                        创建者\n                      </Tag>\n                    )}\n                  </Space>\n                }\n                description={\n                  <Space direction=\"vertical\" size=\"small\">\n                    {team.description && (\n                      <Text type=\"secondary\">{team.description}</Text>\n                    )}\n                    <Space>\n                      <Space size=\"small\">\n                        <UserOutlined />\n                        <Text type=\"secondary\">{team.memberCount} 名成员</Text>\n                      </Space>\n                      <Text type=\"secondary\">\n                        创建于 {new Date(team.createdAt).toLocaleDateString()}\n                      </Text>\n                      <Text type=\"secondary\">\n                        更新于 {new Date(team.updatedAt).toLocaleDateString()}\n                      </Text>\n                    </Space>\n                  </Space>\n                }\n              />\n            </List.Item>\n          )}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default TeamListContent;\n", "/**\n * 邀请成员模态框组件\n */\n\nimport React, { useState } from 'react';\nimport {\n  Modal,\n  Form,\n  Input,\n  Button,\n  Space,\n  Tag,\n  Typography,\n  Divider,\n  message,\n  Alert,\n  Progress,\n  List,\n  Tooltip\n} from 'antd';\nimport {\n  PlusOutlined,\n  DeleteOutlined,\n  MailOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  InfoCircleOutlined\n} from '@ant-design/icons';\nimport { TeamService } from '@/services';\nimport type { InviteMembersRequest } from '@/types/api';\n\nconst { Text } = Typography;\n\ninterface InviteMemberModalProps {\n  visible: boolean;\n  onCancel: () => void;\n  onSuccess: () => void;\n}\n\ninterface InviteResult {\n  email: string;\n  success: boolean;\n  error?: string;\n}\n\nconst InviteMemberModal: React.FC<InviteMemberModalProps> = ({\n  visible,\n  onCancel,\n  onSuccess,\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [emails, setEmails] = useState<string[]>(['']);\n  const [form] = Form.useForm();\n  const [inviteResults, setInviteResults] = useState<InviteResult[]>([]);\n  const [showResults, setShowResults] = useState(false);\n\n  const handleAddEmail = () => {\n    if (emails.length < 10) {\n      setEmails([...emails, '']);\n    } else {\n      message.warning('一次最多邀请10个成员');\n    }\n  };\n\n  const handleRemoveEmail = (index: number) => {\n    if (emails.length > 1) {\n      const newEmails = emails.filter((_, i) => i !== index);\n      setEmails(newEmails);\n    }\n  };\n\n  const handleEmailChange = (index: number, value: string) => {\n    const newEmails = [...emails];\n    newEmails[index] = value;\n    setEmails(newEmails);\n  };\n\n  const validateEmail = (email: string) => {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  };\n\n  const handleSubmit = async () => {\n    // 过滤空邮箱并验证格式\n    const validEmails = emails.filter(email => email.trim() !== '');\n    \n    if (validEmails.length === 0) {\n      message.warning('请至少输入一个邮箱地址');\n      return;\n    }\n\n    // 验证邮箱格式\n    const invalidEmails = validEmails.filter(email => !validateEmail(email));\n    if (invalidEmails.length > 0) {\n      message.error(`以下邮箱格式不正确: ${invalidEmails.join(', ')}`);\n      return;\n    }\n\n    // 检查重复邮箱\n    const uniqueEmails = [...new Set(validEmails)];\n    if (uniqueEmails.length !== validEmails.length) {\n      message.warning('存在重复的邮箱地址');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setShowResults(false);\n\n      const request: InviteMembersRequest = {\n        emails: uniqueEmails,\n      };\n\n      await TeamService.inviteMembers(request);\n\n      // 模拟邀请结果（实际应该从后端返回）\n      const results: InviteResult[] = uniqueEmails.map(email => ({\n        email,\n        success: true,\n      }));\n\n      setInviteResults(results);\n      setShowResults(true);\n\n      message.success(`成功发送 ${uniqueEmails.length} 份邀请`);\n\n      // 延迟关闭模态框，让用户看到结果\n      setTimeout(() => {\n        onSuccess();\n        handleCancel();\n      }, 2000);\n\n    } catch (error: any) {\n      console.error('邀请成员失败:', error);\n\n      // 处理部分成功的情况\n      const results: InviteResult[] = uniqueEmails.map(email => ({\n        email,\n        success: false,\n        error: error.message || '邀请失败',\n      }));\n\n      setInviteResults(results);\n      setShowResults(true);\n      message.error('邀请发送失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCancel = () => {\n    setEmails(['']);\n    setInviteResults([]);\n    setShowResults(false);\n    form.resetFields();\n    onCancel();\n  };\n\n  return (\n    <Modal\n      title=\"邀请团队成员\"\n      open={visible}\n      onCancel={handleCancel}\n      footer={\n        showResults ? [\n          <Button key=\"close\" type=\"primary\" onClick={handleCancel}>\n            关闭\n          </Button>,\n        ] : [\n          <Button key=\"cancel\" onClick={handleCancel}>\n            取消\n          </Button>,\n          <Button\n            key=\"submit\"\n            type=\"primary\"\n            loading={loading}\n            onClick={handleSubmit}\n            disabled={emails.filter(email => email.trim() !== '').length === 0}\n          >\n            发送邀请\n          </Button>,\n        ]\n      }\n      width={700}\n    >\n      {!showResults ? (\n        <>\n          <div style={{ marginBottom: 16 }}>\n            <Text type=\"secondary\">\n              输入要邀请的成员邮箱地址，系统将发送邀请邮件给他们。\n            </Text>\n          </div>\n\n          <Alert\n            message=\"邀请说明\"\n            description={\n              <ul style={{ marginTop: 8, paddingLeft: 20, marginBottom: 0 }}>\n                <li>邀请邮件将发送到指定邮箱</li>\n                <li>受邀者需要注册账号后才能加入团队</li>\n                <li>一次最多可邀请10个成员</li>\n                <li>邀请链接有效期为7天</li>\n              </ul>\n            }\n            type=\"info\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n        </>\n      ) : (\n        <div style={{ marginBottom: 16 }}>\n          <Alert\n            message=\"邀请结果\"\n            description={`已处理 ${inviteResults.length} 个邀请`}\n            type={inviteResults.every(r => r.success) ? 'success' : 'warning'}\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n\n          <List\n            size=\"small\"\n            dataSource={inviteResults}\n            renderItem={(result) => (\n              <List.Item>\n                <Space>\n                  {result.success ? (\n                    <CheckCircleOutlined style={{ color: '#52c41a' }} />\n                  ) : (\n                    <ExclamationCircleOutlined style={{ color: '#faad14' }} />\n                  )}\n                  <Text>{result.email}</Text>\n                  {result.success ? (\n                    <Tag color=\"success\">邀请已发送</Tag>\n                  ) : (\n                    <Tooltip title={result.error}>\n                      <Tag color=\"warning\">发送失败</Tag>\n                    </Tooltip>\n                  )}\n                </Space>\n              </List.Item>\n            )}\n          />\n        </div>\n      )}\n\n      {!showResults && (\n        <Form form={form} layout=\"vertical\">\n          <Form.Item label=\"邀请邮箱\">\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              {emails.map((email, index) => (\n                <Space key={index} style={{ width: '100%' }}>\n                  <Input\n                    placeholder=\"请输入邮箱地址\"\n                    value={email}\n                    onChange={(e) => handleEmailChange(index, e.target.value)}\n                    prefix={<MailOutlined />}\n                    style={{ flex: 1 }}\n                    status={email && !validateEmail(email) ? 'error' : ''}\n                  />\n                  {emails.length > 1 && (\n                    <Button\n                      type=\"text\"\n                      danger\n                      icon={<DeleteOutlined />}\n                      onClick={() => handleRemoveEmail(index)}\n                    />\n                  )}\n                </Space>\n              ))}\n            </Space>\n          </Form.Item>\n\n          {emails.length < 10 && (\n            <Button\n              type=\"dashed\"\n              icon={<PlusOutlined />}\n              onClick={handleAddEmail}\n              style={{ width: '100%', marginBottom: 16 }}\n            >\n              添加更多邮箱\n            </Button>\n          )}\n\n          {emails.filter(email => email.trim() !== '').length > 0 && (\n            <div style={{ marginTop: 16 }}>\n              <Text strong>待邀请邮箱：</Text>\n              <div style={{ marginTop: 8 }}>\n                {emails\n                  .filter(email => email.trim() !== '')\n                  .map((email, index) => (\n                    <Tag\n                      key={index}\n                      color={validateEmail(email) ? 'blue' : 'red'}\n                      style={{ marginBottom: 4 }}\n                    >\n                      {email}\n                    </Tag>\n                  ))}\n              </div>\n            </div>\n          )}\n        </Form>\n      )}\n    </Modal>\n  );\n};\n\nexport default InviteMemberModal;\n", "/**\n * 成员分配模态框组件\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  Modal, \n  Form, \n  Select, \n  Button, \n  Space, \n  Typography, \n  message,\n  Alert,\n  List,\n  Avatar,\n  Tag,\n  Divider,\n  Transfer\n} from 'antd';\nimport { \n  UserOutlined, \n  TeamOutlined,\n  SwapOutlined,\n  CheckCircleOutlined\n} from '@ant-design/icons';\nimport { TeamService } from '@/services';\nimport type { TeamMemberResponse, TeamDetailResponse } from '@/types/api';\n\nconst { Text } = Typography;\nconst { Option } = Select;\n\ninterface MemberAssignModalProps {\n  visible: boolean;\n  onCancel: () => void;\n  onSuccess: () => void;\n  currentTeamId: number;\n}\n\ninterface AssignmentTarget {\n  id: number;\n  name: string;\n  type: 'team' | 'project';\n  memberCount?: number;\n}\n\nconst MemberAssignModal: React.FC<MemberAssignModalProps> = ({\n  visible,\n  onCancel,\n  onSuccess,\n  currentTeamId,\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [members, setMembers] = useState<TeamMemberResponse[]>([]);\n  const [availableTargets, setAvailableTargets] = useState<AssignmentTarget[]>([]);\n  const [selectedMembers, setSelectedMembers] = useState<number[]>([]);\n  const [selectedTarget, setSelectedTarget] = useState<number | undefined>();\n  const [form] = Form.useForm();\n\n  useEffect(() => {\n    if (visible) {\n      fetchMembers();\n      fetchAvailableTargets();\n    }\n  }, [visible]);\n\n  const fetchMembers = async () => {\n    try {\n      const response = await TeamService.getTeamMembers({ current: 1, pageSize: 1000 });\n      // 过滤掉创建者，因为创建者不能被分配\n      const assignableMembers = response.list.filter(member => !member.isCreator);\n      setMembers(assignableMembers);\n    } catch (error) {\n      console.error('获取团队成员失败:', error);\n      message.error('获取团队成员失败');\n    }\n  };\n\n  const fetchAvailableTargets = async () => {\n    try {\n      // 获取用户的其他团队作为分配目标\n      const teams = await TeamService.getUserTeams();\n      const otherTeams = teams\n        .filter(team => team.id !== currentTeamId)\n        .map(team => ({\n          id: team.id,\n          name: team.name,\n          type: 'team' as const,\n          memberCount: team.memberCount,\n        }));\n      \n      setAvailableTargets(otherTeams);\n    } catch (error) {\n      console.error('获取可分配目标失败:', error);\n      message.error('获取可分配目标失败');\n    }\n  };\n\n  const handleAssign = async () => {\n    if (selectedMembers.length === 0) {\n      message.warning('请选择要分配的成员');\n      return;\n    }\n\n    if (!selectedTarget) {\n      message.warning('请选择分配目标');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      \n      // 这里应该调用后端API进行成员分配\n      // 由于后端可能没有这个接口，我们模拟一个成功的响应\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      message.success(`成功将 ${selectedMembers.length} 名成员分配到目标团队`);\n      onSuccess();\n      handleCancel();\n      \n    } catch (error) {\n      console.error('分配成员失败:', error);\n      message.error('分配成员失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCancel = () => {\n    setSelectedMembers([]);\n    setSelectedTarget(undefined);\n    form.resetFields();\n    onCancel();\n  };\n\n  const selectedMemberDetails = members.filter(member => \n    selectedMembers.includes(member.id)\n  );\n\n  const selectedTargetDetails = availableTargets.find(target => \n    target.id === selectedTarget\n  );\n\n  return (\n    <Modal\n      title=\"分配团队成员\"\n      open={visible}\n      onCancel={handleCancel}\n      footer={[\n        <Button key=\"cancel\" onClick={handleCancel}>\n          取消\n        </Button>,\n        <Button\n          key=\"submit\"\n          type=\"primary\"\n          loading={loading}\n          onClick={handleAssign}\n          disabled={selectedMembers.length === 0 || !selectedTarget}\n        >\n          确认分配\n        </Button>,\n      ]}\n      width={800}\n    >\n      <Alert\n        message=\"成员分配说明\"\n        description=\"将选中的成员从当前团队分配到其他团队。分配后，成员将离开当前团队并加入目标团队。\"\n        type=\"info\"\n        showIcon\n        style={{ marginBottom: 16 }}\n      />\n\n      <Form form={form} layout=\"vertical\">\n        <Form.Item label=\"选择要分配的成员\">\n          <div style={{ maxHeight: 200, overflowY: 'auto', border: '1px solid #d9d9d9', borderRadius: 6, padding: 8 }}>\n            {members.length === 0 ? (\n              <Text type=\"secondary\">暂无可分配的成员</Text>\n            ) : (\n              <List\n                size=\"small\"\n                dataSource={members}\n                renderItem={(member) => (\n                  <List.Item\n                    style={{ \n                      cursor: 'pointer',\n                      backgroundColor: selectedMembers.includes(member.id) ? '#e6f7ff' : 'transparent',\n                      padding: '8px 12px',\n                      borderRadius: 4,\n                      margin: '2px 0'\n                    }}\n                    onClick={() => {\n                      if (selectedMembers.includes(member.id)) {\n                        setSelectedMembers(prev => prev.filter(id => id !== member.id));\n                      } else {\n                        setSelectedMembers(prev => [...prev, member.id]);\n                      }\n                    }}\n                  >\n                    <List.Item.Meta\n                      avatar={<Avatar size=\"small\" icon={<UserOutlined />} />}\n                      title={\n                        <Space>\n                          {member.name}\n                          {selectedMembers.includes(member.id) && (\n                            <CheckCircleOutlined style={{ color: '#1890ff' }} />\n                          )}\n                        </Space>\n                      }\n                      description={member.email}\n                    />\n                    <Tag color={member.isActive ? 'green' : 'red'}>\n                      {member.isActive ? '活跃' : '停用'}\n                    </Tag>\n                  </List.Item>\n                )}\n              />\n            )}\n          </div>\n        </Form.Item>\n\n        <Form.Item label=\"选择分配目标\">\n          <Select\n            placeholder=\"请选择目标团队\"\n            value={selectedTarget}\n            onChange={setSelectedTarget}\n            style={{ width: '100%' }}\n          >\n            {availableTargets.map(target => (\n              <Option key={target.id} value={target.id}>\n                <Space>\n                  <TeamOutlined />\n                  {target.name}\n                  <Text type=\"secondary\">({target.memberCount} 名成员)</Text>\n                </Space>\n              </Option>\n            ))}\n          </Select>\n        </Form.Item>\n      </Form>\n\n      {selectedMembers.length > 0 && selectedTargetDetails && (\n        <>\n          <Divider />\n          <div>\n            <Text strong>分配预览：</Text>\n            <div style={{ marginTop: 8, padding: 12, background: '#f5f5f5', borderRadius: 6 }}>\n              <Space direction=\"vertical\" style={{ width: '100%' }}>\n                <div>\n                  <Text>将以下 {selectedMemberDetails.length} 名成员：</Text>\n                  <div style={{ marginTop: 4 }}>\n                    {selectedMemberDetails.map(member => (\n                      <Tag key={member.id} color=\"blue\" style={{ margin: '2px' }}>\n                        {member.name}\n                      </Tag>\n                    ))}\n                  </div>\n                </div>\n                <div style={{ textAlign: 'center' }}>\n                  <SwapOutlined style={{ fontSize: 16, color: '#1890ff' }} />\n                </div>\n                <div>\n                  <Text>分配到团队：</Text>\n                  <Tag color=\"green\" style={{ marginLeft: 8 }}>\n                    {selectedTargetDetails.name}\n                  </Tag>\n                </div>\n              </Space>\n            </div>\n          </div>\n        </>\n      )}\n    </Modal>\n  );\n};\n\nexport default MemberAssignModal;\n", "/**\n * 团队详情内容组件\n */\n\nimport React, { useState } from 'react';\nimport { \n  Descriptions, \n  Button, \n  Space, \n  Typography, \n  message, \n  Modal, \n  Form, \n  Input,\n  Spin,\n  Divider,\n  Empty\n} from 'antd';\nimport {\n  TeamOutlined,\n  EditOutlined,\n  UserAddOutlined,\n  SettingOutlined,\n  SwapOutlined\n} from '@ant-design/icons';\nimport { TeamService } from '@/services';\nimport type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';\nimport TeamMemberList from './TeamMemberList';\nimport InviteMemberModal from './InviteMemberModal';\nimport MemberAssignModal from './MemberAssignModal';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\n\ninterface TeamDetailContentProps {\n  teamDetail: TeamDetailResponse | null;\n  loading: boolean;\n  onRefresh: () => void;\n}\n\nconst TeamDetailContent: React.FC<TeamDetailContentProps> = ({\n  teamDetail,\n  loading,\n  onRefresh\n}) => {\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [inviteModalVisible, setInviteModalVisible] = useState(false);\n  const [assignModalVisible, setAssignModalVisible] = useState(false);\n  const [updating, setUpdating] = useState(false);\n  const [form] = Form.useForm();\n\n  const handleEdit = () => {\n    if (!teamDetail) return;\n    form.setFieldsValue({\n      name: teamDetail.name,\n      description: teamDetail.description,\n    });\n    setEditModalVisible(true);\n  };\n\n  const handleUpdate = async (values: any) => {\n    if (!teamDetail) return;\n    \n    try {\n      setUpdating(true);\n      const updateData: UpdateTeamRequest = {\n        name: values.name,\n        description: values.description,\n      };\n      \n      await TeamService.updateTeam(teamDetail.id, updateData);\n      setEditModalVisible(false);\n      message.success('团队信息更新成功');\n      onRefresh();\n    } catch (error) {\n      console.error('更新团队信息失败:', error);\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px 0' }}>\n        <Spin size=\"large\" />\n      </div>\n    );\n  }\n\n  if (!teamDetail) {\n    return (\n      <Empty\n        image={Empty.PRESENTED_IMAGE_SIMPLE}\n        description=\"请先选择一个团队\"\n      />\n    );\n  }\n\n  return (\n    <div>\n      {/* 团队基本信息 */}\n      <div style={{ marginBottom: 24 }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>\n          <Space>\n            <TeamOutlined style={{ fontSize: 24, color: '#1890ff' }} />\n            <Title level={3} style={{ margin: 0 }}>\n              {teamDetail.name}\n            </Title>\n            {teamDetail.isCreator && (\n              <Text type=\"secondary\" style={{ fontSize: 14 }}>\n                (管理员)\n              </Text>\n            )}\n          </Space>\n          {teamDetail.isCreator && (\n            <Space>\n              <Button\n                type=\"primary\"\n                icon={<UserAddOutlined />}\n                onClick={() => setInviteModalVisible(true)}\n              >\n                邀请成员\n              </Button>\n              <Button\n                icon={<SwapOutlined />}\n                onClick={() => setAssignModalVisible(true)}\n              >\n                分配成员\n              </Button>\n              <Button\n                icon={<EditOutlined />}\n                onClick={handleEdit}\n              >\n                编辑团队\n              </Button>\n            </Space>\n          )}\n        </div>\n\n        <Descriptions column={2} bordered>\n          <Descriptions.Item label=\"团队名称\">\n            {teamDetail.name}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"成员数量\">\n            {teamDetail.memberCount} 人\n          </Descriptions.Item>\n          <Descriptions.Item label=\"创建时间\">\n            {new Date(teamDetail.createdAt).toLocaleString()}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"更新时间\">\n            {new Date(teamDetail.updatedAt).toLocaleString()}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"团队描述\" span={2}>\n            {teamDetail.description || '暂无描述'}\n          </Descriptions.Item>\n        </Descriptions>\n      </div>\n\n      <Divider />\n\n      {/* 团队成员列表 */}\n      <TeamMemberList teamId={teamDetail.id} isCreator={teamDetail.isCreator} />\n\n      {/* 编辑团队模态框 */}\n      <Modal\n        title=\"编辑团队信息\"\n        open={editModalVisible}\n        onCancel={() => setEditModalVisible(false)}\n        footer={null}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleUpdate}\n        >\n          <Form.Item\n            label=\"团队名称\"\n            name=\"name\"\n            rules={[\n              { required: true, message: '请输入团队名称' },\n              { min: 2, max: 50, message: '团队名称长度应在2-50个字符之间' }\n            ]}\n          >\n            <Input placeholder=\"请输入团队名称\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"团队描述\"\n            name=\"description\"\n            rules={[\n              { max: 200, message: '团队描述不能超过200个字符' }\n            ]}\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请输入团队描述（可选）\"\n              showCount\n              maxLength={200}\n            />\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => setEditModalVisible(false)}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\" loading={updating}>\n                保存\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 邀请成员模态框 */}\n      <InviteMemberModal\n        visible={inviteModalVisible}\n        onCancel={() => setInviteModalVisible(false)}\n        teamId={teamDetail.id}\n        onSuccess={() => {\n          setInviteModalVisible(false);\n          onRefresh();\n        }}\n      />\n\n      {/* 分配成员模态框 */}\n      <MemberAssignModal\n        visible={assignModalVisible}\n        onCancel={() => setAssignModalVisible(false)}\n        teamId={teamDetail.id}\n        onSuccess={() => {\n          setAssignModalVisible(false);\n          onRefresh();\n        }}\n      />\n    </div>\n  );\n};\n\nexport default TeamDetailContent;\n", "/**\n * 团队成员列表组件\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Avatar,\n  Tag,\n  Button,\n  Space,\n  message,\n  Modal,\n  Input,\n  Tooltip,\n  Dropdown,\n  Checkbox,\n  Typography,\n  Badge,\n  Divider,\n  Select\n} from 'antd';\nimport {\n  UserOutlined,\n  CrownOutlined,\n  DeleteOutlined,\n  SearchOutlined,\n  MoreOutlined,\n  UserSwitchOutlined,\n  StopOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  FilterOutlined\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport type { MenuProps } from 'antd';\nimport { TeamService } from '@/services';\nimport type { TeamMemberResponse } from '@/types/api';\n\nconst { Text } = Typography;\nconst { Option } = Select;\n\ninterface TeamMemberListProps {\n  teamId: number;\n  isCreator: boolean;\n  onMemberChange?: () => void;\n}\n\nconst TeamMemberList: React.FC<TeamMemberListProps> = ({\n  teamId,\n  isCreator,\n  onMemberChange,\n}) => {\n  const [loading, setLoading] = useState(true);\n  const [members, setMembers] = useState<TeamMemberResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n  const [filteredMembers, setFilteredMembers] = useState<TeamMemberResponse[]>([]);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [statusFilter, setStatusFilter] = useState<string>('all');\n\n  useEffect(() => {\n    fetchMembers();\n  }, [teamId]);\n\n  useEffect(() => {\n    // 过滤成员列表 - 添加空值检查\n    if (!members || !Array.isArray(members)) {\n      setFilteredMembers([]);\n      return;\n    }\n\n    const filtered = members.filter(member => {\n      // 确保member对象存在且有必要的属性\n      if (!member || !member.name || !member.email) {\n        return false;\n      }\n\n      const matchesSearch = !searchText ||\n        member.name.toLowerCase().includes(searchText.toLowerCase()) ||\n        member.email.toLowerCase().includes(searchText.toLowerCase());\n\n      const matchesStatus = statusFilter === 'all' ||\n        (statusFilter === 'active' && member.isActive) ||\n        (statusFilter === 'inactive' && !member.isActive) ||\n        (statusFilter === 'creator' && member.isCreator) ||\n        (statusFilter === 'member' && !member.isCreator);\n\n      return matchesSearch && matchesStatus;\n    });\n    setFilteredMembers(filtered);\n  }, [members, searchText, statusFilter]);\n\n  const fetchMembers = async () => {\n    try {\n      setLoading(true);\n      const response = await TeamService.getTeamMembers({ current: 1, pageSize: 1000 });\n      // 确保返回的数据是数组格式\n      setMembers(response?.list || []);\n    } catch (error) {\n      console.error('获取团队成员失败:', error);\n      message.error('获取团队成员失败');\n      // 出错时设置为空数组\n      setMembers([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRemoveMember = (member: TeamMemberResponse) => {\n    if (member.isCreator) {\n      message.warning('不能移除团队创建者');\n      return;\n    }\n\n    Modal.confirm({\n      title: '确认移除成员',\n      content: `确定要移除成员 \"${member.name}\" 吗？`,\n      okText: '确认',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          await TeamService.removeMember(member.id);\n          message.success('成员移除成功');\n          fetchMembers();\n          onMemberChange?.();\n        } catch (error) {\n          console.error('移除成员失败:', error);\n        }\n      },\n    });\n  };\n\n  const handleBatchRemove = () => {\n    const selectedMembers = members.filter(member =>\n      selectedRowKeys.includes(member.id) && !member.isCreator\n    );\n\n    if (selectedMembers.length === 0) {\n      message.warning('请选择要移除的成员');\n      return;\n    }\n\n    Modal.confirm({\n      title: '批量移除成员',\n      content: `确定要移除选中的 ${selectedMembers.length} 名成员吗？`,\n      okText: '确认',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          await Promise.all(\n            selectedMembers.map(member => TeamService.removeMember(member.id))\n          );\n          message.success(`成功移除 ${selectedMembers.length} 名成员`);\n          setSelectedRowKeys([]);\n          fetchMembers();\n          onMemberChange?.();\n        } catch (error) {\n          console.error('批量移除成员失败:', error);\n          message.error('批量移除失败');\n        }\n      },\n    });\n  };\n\n  const columns: ColumnsType<TeamMemberResponse> = [\n    {\n      title: '成员',\n      dataIndex: 'name',\n      key: 'name',\n      render: (name, record) => (\n        <Space>\n          <Avatar size=\"small\" icon={<UserOutlined />} />\n          <div>\n            <div>{name}</div>\n            <div style={{ fontSize: 12, color: '#999' }}>{record.email}</div>\n          </div>\n        </Space>\n      ),\n    },\n    {\n      title: '角色',\n      dataIndex: 'isCreator',\n      key: 'role',\n      width: 100,\n      render: (isCreator) => (\n        <Tag color={isCreator ? 'gold' : 'blue'} icon={isCreator ? <CrownOutlined /> : <UserOutlined />}>\n          {isCreator ? '创建者' : '成员'}\n        </Tag>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'isActive',\n      key: 'status',\n      width: 80,\n      render: (isActive) => (\n        <Tag color={isActive ? 'green' : 'red'}>\n          {isActive ? '活跃' : '停用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '加入时间',\n      dataIndex: 'assignedAt',\n      key: 'assignedAt',\n      width: 150,\n      render: (assignedAt) => new Date(assignedAt).toLocaleDateString(),\n    },\n    {\n      title: '最后访问',\n      dataIndex: 'lastAccessTime',\n      key: 'lastAccessTime',\n      width: 150,\n      render: (lastAccessTime) => {\n        const date = new Date(lastAccessTime);\n        const now = new Date();\n        const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));\n        \n        let color = 'green';\n        if (diffDays > 7) color = 'orange';\n        if (diffDays > 30) color = 'red';\n        \n        return (\n          <Tooltip title={date.toLocaleString()}>\n            <Tag color={color}>\n              {diffDays === 0 ? '今天' : `${diffDays}天前`}\n            </Tag>\n          </Tooltip>\n        );\n      },\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 120,\n      render: (_, record) => {\n        if (!isCreator || record.isCreator) {\n          return <Text type=\"secondary\">-</Text>;\n        }\n\n        const menuItems: MenuProps['items'] = [\n          {\n            key: 'remove',\n            label: '移除成员',\n            icon: <DeleteOutlined />,\n            danger: true,\n            onClick: () => handleRemoveMember(record),\n          },\n        ];\n\n        return (\n          <Space size=\"small\">\n            <Button\n              type=\"text\"\n              danger\n              size=\"small\"\n              icon={<DeleteOutlined />}\n              onClick={() => handleRemoveMember(record)}\n            >\n              移除\n            </Button>\n            <Dropdown menu={{ items: menuItems }} trigger={['click']}>\n              <Button\n                type=\"text\"\n                size=\"small\"\n                icon={<MoreOutlined />}\n              />\n            </Dropdown>\n          </Space>\n        );\n      },\n    },\n  ];\n\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: (newSelectedRowKeys: React.Key[]) => {\n      setSelectedRowKeys(newSelectedRowKeys);\n    },\n    getCheckboxProps: (record: TeamMemberResponse) => ({\n      disabled: record.isCreator, // 创建者不能被选中\n    }),\n  };\n\n  return (\n    <Card\n      title={\n        <Space>\n          <Text strong>团队成员</Text>\n          <Badge count={filteredMembers.length} showZero />\n        </Space>\n      }\n      extra={\n        <Space>\n          <Select\n            value={statusFilter}\n            onChange={setStatusFilter}\n            style={{ width: 120 }}\n            size=\"small\"\n          >\n            <Option value=\"all\">全部</Option>\n            <Option value=\"active\">活跃</Option>\n            <Option value=\"inactive\">停用</Option>\n            <Option value=\"creator\">创建者</Option>\n            <Option value=\"member\">成员</Option>\n          </Select>\n          <Input\n            placeholder=\"搜索成员\"\n            prefix={<SearchOutlined />}\n            value={searchText}\n            onChange={(e) => setSearchText(e.target.value)}\n            style={{ width: 200 }}\n            size=\"small\"\n          />\n        </Space>\n      }\n    >\n      {selectedRowKeys.length > 0 && isCreator && (\n        <div style={{ marginBottom: 16, padding: 12, background: '#f5f5f5', borderRadius: 6 }}>\n          <Space>\n            <Text>已选择 {selectedRowKeys.length} 名成员</Text>\n            <Button\n              size=\"small\"\n              danger\n              icon={<DeleteOutlined />}\n              onClick={handleBatchRemove}\n            >\n              批量移除\n            </Button>\n            <Button\n              size=\"small\"\n              onClick={() => setSelectedRowKeys([])}\n            >\n              取消选择\n            </Button>\n          </Space>\n        </div>\n      )}\n\n      <Table\n        columns={columns}\n        dataSource={filteredMembers}\n        rowKey=\"id\"\n        loading={loading}\n        rowSelection={isCreator ? rowSelection : undefined}\n        pagination={{\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total) => `共 ${total} 名成员`,\n          pageSize: 10,\n        }}\n      />\n    </Card>\n  );\n};\n\nexport default TeamMemberList;\n", "/**\n * 用户资料内容组件\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  Form, \n  Input, \n  Button, \n  Space, \n  Typography, \n  message, \n  Divider,\n  Avatar,\n  Upload,\n  Modal\n} from 'antd';\nimport { \n  UserOutlined, \n  EditOutlined, \n  LockOutlined,\n  MailOutlined,\n  SaveOutlined,\n  UploadOutlined\n} from '@ant-design/icons';\nimport { UserService } from '@/services';\nimport type { UserProfileResponse, UpdateUserProfileRequest } from '@/types/api';\n\nconst { Title, Text } = Typography;\n\nconst UserProfileContent: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [editing, setEditing] = useState(false);\n  const [userProfile, setUserProfile] = useState<UserProfileResponse | null>(null);\n  const [passwordModalVisible, setPasswordModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const [passwordForm] = Form.useForm();\n\n  useEffect(() => {\n    fetchUserProfile();\n  }, []);\n\n  const fetchUserProfile = async () => {\n    try {\n      setLoading(true);\n      const profile = await UserService.getUserProfile();\n      setUserProfile(profile);\n      form.setFieldsValue({\n        username: profile.username,\n        email: profile.email,\n        phone: profile.phone,\n        realName: profile.realName,\n      });\n    } catch (error) {\n      console.error('获取用户资料失败:', error);\n      message.error('获取用户资料失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSaveProfile = async (values: any) => {\n    try {\n      setSaving(true);\n      const updateData: UpdateUserProfileRequest = {\n        email: values.email,\n        phone: values.phone,\n        realName: values.realName,\n      };\n      \n      const updatedProfile = await UserService.updateUserProfile(updateData);\n      setUserProfile(updatedProfile);\n      setEditing(false);\n      message.success('个人资料更新成功');\n    } catch (error) {\n      console.error('更新个人资料失败:', error);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleChangePassword = async (values: any) => {\n    try {\n      await UserService.changePassword({\n        oldPassword: values.oldPassword,\n        newPassword: values.newPassword,\n      });\n      setPasswordModalVisible(false);\n      passwordForm.resetFields();\n      message.success('密码修改成功');\n    } catch (error) {\n      console.error('修改密码失败:', error);\n    }\n  };\n\n  const handleCancel = () => {\n    setEditing(false);\n    if (userProfile) {\n      form.setFieldsValue({\n        username: userProfile.username,\n        email: userProfile.email,\n        phone: userProfile.phone,\n        realName: userProfile.realName,\n      });\n    }\n  };\n\n  if (loading || !userProfile) {\n    return <div>加载中...</div>;\n  }\n\n  return (\n    <div>\n      <div style={{ display: 'flex', alignItems: 'flex-start', gap: 24 }}>\n        {/* 头像部分 */}\n        <div style={{ textAlign: 'center' }}>\n          <Avatar size={120} icon={<UserOutlined />} />\n          <div style={{ marginTop: 16 }}>\n            <Upload\n              showUploadList={false}\n              beforeUpload={() => {\n                message.info('头像上传功能暂未实现');\n                return false;\n              }}\n            >\n              <Button icon={<UploadOutlined />} size=\"small\">\n                更换头像\n              </Button>\n            </Upload>\n          </div>\n        </div>\n\n        {/* 表单部分 */}\n        <div style={{ flex: 1 }}>\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>\n            <Title level={4} style={{ margin: 0 }}>\n              <UserOutlined /> 基本信息\n            </Title>\n            {!editing && (\n              <Button\n                type=\"primary\"\n                icon={<EditOutlined />}\n                onClick={() => setEditing(true)}\n              >\n                编辑资料\n              </Button>\n            )}\n          </div>\n\n          <Form\n            form={form}\n            layout=\"vertical\"\n            onFinish={handleSaveProfile}\n            disabled={!editing}\n          >\n            <Form.Item\n              label=\"用户名\"\n              name=\"username\"\n            >\n              <Input \n                prefix={<UserOutlined />} \n                disabled \n                placeholder=\"用户名不可修改\"\n              />\n            </Form.Item>\n\n            <Form.Item\n              label=\"邮箱地址\"\n              name=\"email\"\n              rules={[\n                { required: true, message: '请输入邮箱地址' },\n                { type: 'email', message: '请输入有效的邮箱地址' }\n              ]}\n            >\n              <Input \n                prefix={<MailOutlined />} \n                placeholder=\"请输入邮箱地址\"\n              />\n            </Form.Item>\n\n            <Form.Item\n              label=\"手机号码\"\n              name=\"phone\"\n              rules={[\n                { pattern: /^1[3-9]\\d{9}$/, message: '请输入有效的手机号码' }\n              ]}\n            >\n              <Input placeholder=\"请输入手机号码\" />\n            </Form.Item>\n\n            <Form.Item\n              label=\"真实姓名\"\n              name=\"realName\"\n              rules={[\n                { max: 20, message: '真实姓名不能超过20个字符' }\n              ]}\n            >\n              <Input placeholder=\"请输入真实姓名\" />\n            </Form.Item>\n\n            {editing && (\n              <Form.Item>\n                <Space>\n                  <Button\n                    type=\"primary\"\n                    htmlType=\"submit\"\n                    loading={saving}\n                    icon={<SaveOutlined />}\n                  >\n                    保存修改\n                  </Button>\n                  <Button onClick={handleCancel}>\n                    取消\n                  </Button>\n                </Space>\n              </Form.Item>\n            )}\n          </Form>\n        </div>\n      </div>\n\n      <Divider />\n\n      {/* 安全设置 */}\n      <div>\n        <Title level={4} style={{ marginBottom: 16 }}>\n          <LockOutlined /> 安全设置\n        </Title>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '16px 0' }}>\n          <div>\n            <Text strong>登录密码</Text>\n            <br />\n            <Text type=\"secondary\">定期更换密码可以提高账户安全性</Text>\n          </div>\n          <Button\n            onClick={() => setPasswordModalVisible(true)}\n          >\n            修改密码\n          </Button>\n        </div>\n      </div>\n\n      {/* 修改密码模态框 */}\n      <Modal\n        title=\"修改密码\"\n        open={passwordModalVisible}\n        onCancel={() => {\n          setPasswordModalVisible(false);\n          passwordForm.resetFields();\n        }}\n        footer={null}\n      >\n        <Form\n          form={passwordForm}\n          layout=\"vertical\"\n          onFinish={handleChangePassword}\n        >\n          <Form.Item\n            label=\"当前密码\"\n            name=\"oldPassword\"\n            rules={[{ required: true, message: '请输入当前密码' }]}\n          >\n            <Input.Password placeholder=\"请输入当前密码\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"新密码\"\n            name=\"newPassword\"\n            rules={[\n              { required: true, message: '请输入新密码' },\n              { min: 6, message: '密码长度至少6位' }\n            ]}\n          >\n            <Input.Password placeholder=\"请输入新密码\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"确认新密码\"\n            name=\"confirmPassword\"\n            dependencies={['newPassword']}\n            rules={[\n              { required: true, message: '请确认新密码' },\n              ({ getFieldValue }) => ({\n                validator(_, value) {\n                  if (!value || getFieldValue('newPassword') === value) {\n                    return Promise.resolve();\n                  }\n                  return Promise.reject(new Error('两次输入的密码不一致'));\n                },\n              }),\n            ]}\n          >\n            <Input.Password placeholder=\"请再次输入新密码\" />\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => {\n                setPasswordModalVisible(false);\n                passwordForm.resetFields();\n              }}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\">\n                确认修改\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default UserProfileContent;\n", "/**\n * 用户设置内容组件\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  Card, \n  Form, \n  Select, \n  Switch, \n  Button, \n  Space, \n  Typography, \n  message, \n  Divider,\n  Radio,\n  Alert\n} from 'antd';\nimport { \n  SaveOutlined,\n  GlobalOutlined,\n  BellOutlined,\n  EyeOutlined\n} from '@ant-design/icons';\nimport { UserService } from '@/services';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\ninterface UserPreferences {\n  language: string;\n  timezone: string;\n  theme: 'light' | 'dark' | 'auto';\n  notifications: {\n    email: boolean;\n    push: boolean;\n    teamInvites: boolean;\n    systemUpdates: boolean;\n  };\n}\n\nconst UserSettingsContent: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [preferences, setPreferences] = useState<UserPreferences | null>(null);\n  const [form] = Form.useForm();\n\n  useEffect(() => {\n    fetchUserPreferences();\n  }, []);\n\n  const fetchUserPreferences = async () => {\n    try {\n      setLoading(true);\n      const prefs = await UserService.getUserPreferences();\n      setPreferences(prefs);\n      form.setFieldsValue({\n        language: prefs.language,\n        timezone: prefs.timezone,\n        theme: prefs.theme,\n        emailNotifications: prefs.notifications.email,\n        pushNotifications: prefs.notifications.push,\n        teamInviteNotifications: prefs.notifications.teamInvites,\n        systemUpdateNotifications: prefs.notifications.systemUpdates,\n      });\n    } catch (error) {\n      console.error('获取用户偏好设置失败:', error);\n      message.error('获取用户偏好设置失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSaveSettings = async (values: any) => {\n    try {\n      setSaving(true);\n      const updateData = {\n        language: values.language,\n        timezone: values.timezone,\n        theme: values.theme,\n        notifications: {\n          email: values.emailNotifications,\n          push: values.pushNotifications,\n          teamInvites: values.teamInviteNotifications,\n          systemUpdates: values.systemUpdateNotifications,\n        },\n      };\n      \n      await UserService.updateUserPreferences(updateData);\n      setPreferences({ ...preferences!, ...updateData });\n      message.success('设置保存成功');\n    } catch (error) {\n      console.error('保存设置失败:', error);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  if (loading || !preferences) {\n    return <div>加载中...</div>;\n  }\n\n  return (\n    <Form\n      form={form}\n      layout=\"vertical\"\n      onFinish={handleSaveSettings}\n      initialValues={{\n        language: preferences.language,\n        timezone: preferences.timezone,\n        theme: preferences.theme,\n        emailNotifications: preferences.notifications.email,\n        pushNotifications: preferences.notifications.push,\n        teamInviteNotifications: preferences.notifications.teamInvites,\n        systemUpdateNotifications: preferences.notifications.systemUpdates,\n      }}\n    >\n      {/* 基础设置 */}\n      <Card title={<><GlobalOutlined /> 基础设置</>} style={{ marginBottom: 24 }}>\n        <Form.Item\n          label=\"界面语言\"\n          name=\"language\"\n          tooltip=\"选择您偏好的界面显示语言\"\n        >\n          <Select placeholder=\"请选择语言\">\n            <Option value=\"zh-CN\">简体中文</Option>\n            <Option value=\"en-US\">English</Option>\n            <Option value=\"ja-JP\">日本語</Option>\n          </Select>\n        </Form.Item>\n\n        <Form.Item\n          label=\"时区设置\"\n          name=\"timezone\"\n          tooltip=\"选择您所在的时区，影响时间显示\"\n        >\n          <Select placeholder=\"请选择时区\">\n            <Option value=\"Asia/Shanghai\">中国标准时间 (UTC+8)</Option>\n            <Option value=\"America/New_York\">美国东部时间 (UTC-5)</Option>\n            <Option value=\"Europe/London\">英国时间 (UTC+0)</Option>\n            <Option value=\"Asia/Tokyo\">日本标准时间 (UTC+9)</Option>\n          </Select>\n        </Form.Item>\n      </Card>\n\n      {/* 外观设置 */}\n      <Card title={<><EyeOutlined /> 外观设置</>} style={{ marginBottom: 24 }}>\n        <Form.Item\n          label=\"主题模式\"\n          name=\"theme\"\n          tooltip=\"选择您偏好的界面主题\"\n        >\n          <Radio.Group>\n            <Radio value=\"light\">浅色模式</Radio>\n            <Radio value=\"dark\">深色模式</Radio>\n            <Radio value=\"auto\">跟随系统</Radio>\n          </Radio.Group>\n        </Form.Item>\n\n        <Alert\n          message=\"主题设置\"\n          description=\"深色模式可以减少眼部疲劳，特别适合在光线较暗的环境中使用。\"\n          type=\"info\"\n          showIcon\n          style={{ marginTop: 16 }}\n        />\n      </Card>\n\n      {/* 通知设置 */}\n      <Card title={<><BellOutlined /> 通知设置</>} style={{ marginBottom: 24 }}>\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <div>\n              <Text strong>邮件通知</Text>\n              <br />\n              <Text type=\"secondary\">接收重要事件的邮件通知</Text>\n            </div>\n            <Form.Item name=\"emailNotifications\" valuePropName=\"checked\" style={{ margin: 0 }}>\n              <Switch />\n            </Form.Item>\n          </div>\n\n          <Divider style={{ margin: '12px 0' }} />\n\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <div>\n              <Text strong>浏览器推送通知</Text>\n              <br />\n              <Text type=\"secondary\">在浏览器中接收实时通知</Text>\n            </div>\n            <Form.Item name=\"pushNotifications\" valuePropName=\"checked\" style={{ margin: 0 }}>\n              <Switch />\n            </Form.Item>\n          </div>\n\n          <Divider style={{ margin: '12px 0' }} />\n\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <div>\n              <Text strong>团队邀请通知</Text>\n              <br />\n              <Text type=\"secondary\">当有人邀请您加入团队时通知</Text>\n            </div>\n            <Form.Item name=\"teamInviteNotifications\" valuePropName=\"checked\" style={{ margin: 0 }}>\n              <Switch />\n            </Form.Item>\n          </div>\n\n          <Divider style={{ margin: '12px 0' }} />\n\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <div>\n              <Text strong>系统更新通知</Text>\n              <br />\n              <Text type=\"secondary\">接收系统功能更新和维护通知</Text>\n            </div>\n            <Form.Item name=\"systemUpdateNotifications\" valuePropName=\"checked\" style={{ margin: 0 }}>\n              <Switch />\n            </Form.Item>\n          </div>\n        </Space>\n      </Card>\n\n      {/* 保存按钮 */}\n      <Card>\n        <Form.Item>\n          <Space>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={saving}\n              icon={<SaveOutlined />}\n              size=\"large\"\n            >\n              保存设置\n            </Button>\n            <Button\n              onClick={() => {\n                form.resetFields();\n                message.info('已重置为上次保存的设置');\n              }}\n            >\n              重置\n            </Button>\n          </Space>\n        </Form.Item>\n      </Card>\n    </Form>\n  );\n};\n\nexport default UserSettingsContent;\n"], "names": [], "mappings": ";;;AAAA;;CAEC;;;;4BAmSD;;;eAAA;;;;;;wEAjS2C;6BAapC;8BAOA;iCAE6B;4BAED;;;;;;;;;;AAEnC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAQlC,MAAM,4BAAsE,CAAC,EAC3E,mBAAmB,EACnB,OAAO,EACP,SAAS,EACV;;IACC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,IAAA,eAAQ,EAAyB,EAAE;IACzF,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAM;IAChD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,IAAA,eAAQ,EAAC;IAE/D,IAAA,gBAAS,EAAC;QACR,IAAI,qBAAqB;YACvB;YACA;QACF;IACF,GAAG;QAAC;KAAoB;IAExB,MAAM,2BAA2B;QAC/B,IAAI;YACF,MAAM,UAAU,MAAM,6BAAmB,CAAC,sBAAsB;YAChE,uBAAuB;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,QAAQ,MAAM,6BAAmB,CAAC,YAAY;YACpD,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,qBAAqB;QAE1B,IAAI;YACF,MAAM,6BAAmB,CAAC,iBAAiB,CAAC,oBAAoB,EAAE;YAClE,aAAO,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,qBAAqB;QAE1B,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,MAAM;gBACJ,IAAI;oBACF,MAAM,6BAAmB,CAAC,kBAAkB,CAAC,oBAAoB,EAAE;oBACnE,aAAO,CAAC,OAAO,CAAC;oBAChB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,WAAW;gBAC3B;YACF;QACF;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,YAAY;YAChB,CAAC,uBAAkB,CAAC,MAAM,CAAC,EAAE;gBAAE,OAAO;gBAAS,MAAM;YAAK;YAC1D,CAAC,uBAAkB,CAAC,OAAO,CAAC,EAAE;gBAAE,OAAO;gBAAO,MAAM;YAAM;YAC1D,CAAC,uBAAkB,CAAC,SAAS,CAAC,EAAE;gBAAE,OAAO;gBAAW,MAAM;YAAM;YAChE,CAAC,uBAAkB,CAAC,OAAO,CAAC,EAAE;gBAAE,OAAO;gBAAU,MAAM;YAAM;QAC/D;QAEA,MAAM,SAAS,SAAS,CAAC,OAAO,IAAI;YAAE,OAAO;YAAW,MAAM;QAAK;QACnE,qBAAO,2BAAC,SAAG;YAAC,OAAO,OAAO,KAAK;sBAAG,OAAO,IAAI;;;;;;IAC/C;IAEA,MAAM,iBAAoD;QACxD;YACE,OAAO;YACP,WAAW;gBAAC;gBAAQ;aAAO;YAC3B,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,QAAkB,CAAC,CAAC,EAAE,MAAM,CAAC;QACxC;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,SAA+B,aAAa;QACvD;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,OAAiB,IAAI,KAAK,MAAM,kBAAkB;QAC7D;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,OAAiB,IAAI,KAAK,MAAM,kBAAkB;QAC7D;KACD;IAED,IAAI,SACF,qBAAO,2BAAC;kBAAI;;;;;;IAGd,IAAI,CAAC,qBACH,qBACE,2BAAC,WAAK;QACJ,OAAO,WAAK,CAAC,sBAAsB;QACnC,aAAY;kBAEZ,cAAA,2BAAC;YAAK,MAAK;sBAAY;;;;;;;;;;;IAO7B,qBACE,2BAAC;;0BAEC,2BAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAG;;kCAC7B,2BAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,gBAAgB;4BAAiB,YAAY;4BAAU,cAAc;wBAAG;;0CACrG,2BAAC;gCAAM,OAAO;gCAAG,OAAO;oCAAE,QAAQ;gCAAE;;kDAClC,2BAAC,oBAAa;;;;;oCAAG;;;;;;;0CAEnB,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCACL,oBAAM,2BAAC,qBAAc;;;;;wCACrB,SAAS;kDACV;;;;;;kDAGD,2BAAC,YAAM;wCACL,oBAAM,2BAAC,sBAAe;;;;;wCACtB,SAAS,IAAM,uBAAuB;kDACvC;;;;;;;;;;;;;;;;;;kCAML,2BAAC,kBAAY;wBAAC,QAAQ;wBAAG,QAAQ;;0CAC/B,2BAAC,kBAAY,CAAC,IAAI;gCAAC,OAAM;0CACtB,oBAAoB,IAAI,CAAC,IAAI;;;;;;0CAEhC,2BAAC,kBAAY,CAAC,IAAI;gCAAC,OAAM;0CACtB,aAAa,oBAAoB,MAAM;;;;;;0CAE1C,2BAAC,kBAAY,CAAC,IAAI;gCAAC,OAAM;0CACtB,IAAI,KAAK,oBAAoB,SAAS,EAAE,kBAAkB;;;;;;0CAE7D,2BAAC,kBAAY,CAAC,IAAI;gCAAC,OAAM;0CACtB,IAAI,KAAK,oBAAoB,OAAO,EAAE,kBAAkB;;;;;;0CAE3D,2BAAC,kBAAY,CAAC,IAAI;gCAAC,OAAM;;oCAAM;oCAC3B,oBAAoB,UAAU;;;;;;;0CAElC,2BAAC,kBAAY,CAAC,IAAI;gCAAC,OAAM;;oCACtB,oBAAoB,IAAI,CAAC,OAAO;oCAAC;;;;;;;;;;;;;kCAKtC,2BAAC;wBAAI,OAAO;4BAAE,WAAW;wBAAG;kCAC1B,cAAA,2BAAC,WAAK;;gCACH,oBAAoB,MAAM,KAAK,uBAAkB,CAAC,MAAM,kBACvD;;sDACE,2BAAC,YAAM;4CACL,MAAK;4CACL,oBAAM,2BAAC,iBAAU;;;;;4CACjB,SAAS;sDACV;;;;;;sDAGD,2BAAC,YAAM;4CACL,MAAM;4CACN,oBAAM,2BAAC,mBAAY;;;;;4CACnB,SAAS;sDACV;;;;;;;;gCAKJ,oBAAoB,MAAM,KAAK,uBAAkB,CAAC,OAAO,kBACxD,2BAAC,YAAM;oCACL,MAAK;oCACL,oBAAM,2BAAC,qBAAc;;;;;oCACrB,SAAS;8CACV;;;;;;;;;;;;;;;;;;;;;;;YASR,2BACC,2BAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAG;;kCAC7B,2BAAC;wBAAM,OAAO;kCAAG;;;;;;kCACjB,2BAAC;wBAAI,OAAO;4BAAE,cAAc;wBAAG;;0CAC7B,2BAAC;0CAAK;;;;;;0CACN,2BAAC,cAAQ;gCACP,SAAS,KAAK,KAAK,CAAC,AAAC,UAAU,WAAW,GAAG,UAAU,YAAY,GAAI;gCACvE,QAAQ,IAAM,CAAC,EAAE,UAAU,WAAW,CAAC,KAAK,EAAE,UAAU,YAAY,CAAC,EAAE,CAAC;gCACxE,OAAO;oCAAE,WAAW;gCAAE;;;;;;;;;;;;oBAIzB,UAAU,WAAW,GAAG,UAAU,YAAY,GAAG,qBAChD,2BAAC,WAAK;wBACJ,SAAQ;wBACR,aAAY;wBACZ,MAAK;wBACL,QAAQ;wBACR,OAAO;4BAAE,WAAW;wBAAG;;;;;;;;;;;;0BAO/B,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU,IAAM,uBAAuB;gBACvC,QAAQ;gBACR,OAAO;0BAEP,cAAA,2BAAC,WAAK;oBACJ,SAAS;oBACT,YAAY;oBACZ,QAAO;oBACP,YAAY;wBACV,iBAAiB;wBACjB,iBAAiB;wBACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;oBACxC;;;;;;;;;;;;;;;;;AAKV;GA7PM;KAAA;IA+PN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrSf;;CAEC;;;;4BA6SD;;;eAAA;;;;;;wEA3S2C;6BAapC;8BAMA;iCAC6B;;;;;;;;;;AAGpC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAMlC,MAAM,2BAAoE,CAAC,EACzE,qBAAqB,EACtB;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAA6B,EAAE;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAAC;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAkC;IAClF,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,IAAA,eAAQ,EAAC;IACnE,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IAEzC,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,6BAAmB,CAAC,cAAc;YACzD,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;QAChB,YAAY;QACZ,yBAAyB;IAC3B;IAEA,MAAM,yBAAyB;QAC7B,IAAI,CAAC,cAAc;QAEnB,IAAI;YACF,eAAe;YACf,MAAM,UAAqC;gBACzC,QAAQ,aAAa,EAAE;gBACvB;YACF;YAEA,MAAM,6BAAmB,CAAC,kBAAkB,CAAC;YAC7C,yBAAyB;YACzB,aAAO,CAAC,OAAO,CAAC;YAChB,kCAAA,oCAAA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,WAAW;YACf,CAAC,OAAO,EAAE,KAAK,OAAO,CAAC,EAAE,CAAC;YAC1B;YACA;YACA;SACD;QAED,IAAI,KAAK,KAAK,GAAG,GAAG;YAClB,SAAS,IAAI,CAAC;YACd,SAAS,IAAI,CAAC;QAChB;QAEA,IAAI,KAAK,KAAK,IAAI,KAAK;YACrB,SAAS,IAAI,CAAC;YACd,SAAS,IAAI,CAAC;YACd,SAAS,IAAI,CAAC;QAChB;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,KAAK,KAAK,KAAK,GAAG,OAAO,WAAW,WAAW;QACnD,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,WAAW,WAAW;QACnD,OAAO,WAAW,WAAW;IAC/B;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,KAAK,KAAK,KAAK,GAAG,qBAAO,2BAAC,oBAAa;;;;;QAC3C,IAAI,KAAK,KAAK,GAAG,KAAK,qBAAO,2BAAC,mBAAY;;;;;QAC1C,qBAAO,2BAAC,oBAAa;;;;;IACvB;IAEA,MAAM,iBAAiB,CAAC,MAAgC;QACtD,OAAO,6BAAmB,CAAC,kBAAkB,CAAC,MAAM;IACtD;IAEA,qBACE,2BAAC;;0BACC,2BAAC,SAAG;gBAAC,QAAQ;oBAAC;oBAAI;iBAAG;0BAClB,MAAM,GAAG,CAAC,CAAC;oBACV,MAAM,WAAW,gBAAgB;oBACjC,MAAM,QAAQ,aAAa;oBAC3B,MAAM,OAAO,YAAY;oBACzB,MAAM,YAAY,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,GAAG;oBAEjD,qBACE,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,cAAA,2BAAC,UAAI;4BACH,SAAS;4BACT,SAAS;4BACT,OAAO;gCACL,QAAQ;gCACR,aAAa,YAAY,YAAY;gCACrC,UAAU;4BACZ;;gCAEC,2BACC,2BAAC;oCACC,OAAO;wCACL,UAAU;wCACV,KAAK;wCACL,OAAO;wCACP,YAAY;wCACZ,OAAO;wCACP,SAAS;wCACT,cAAc;wCACd,UAAU;wCACV,YAAY;oCACd;8CACD;;;;;;8CAKH,2BAAC;oCAAI,OAAO;wCAAE,WAAW;wCAAU,cAAc;oCAAG;;sDAClD,2BAAC;4CAAI,OAAO;gDAAE,UAAU;gDAAI;gDAAO,cAAc;4CAAG;sDACjD;;;;;;sDAEH,2BAAC;4CAAM,OAAO;4CAAG,OAAO;gDAAE,QAAQ;gDAAG;4CAAM;sDACxC,KAAK,IAAI;;;;;;sDAEZ,2BAAC;4CAAK,MAAK;sDAAa,KAAK,WAAW;;;;;;;;;;;;8CAG1C,2BAAC;oCAAI,OAAO;wCAAE,WAAW;wCAAU,cAAc;oCAAG;;sDAClD,2BAAC;4CAAI,OAAO;gDAAE,UAAU;gDAAI,YAAY;gDAAQ;4CAAM;;gDAAG;gDACrD,KAAK,KAAK;8DACZ,2BAAC;oDAAK,OAAO;wDAAE,UAAU;wDAAI,YAAY;oDAAS;8DAAG;;;;;;;;;;;;wCAEtD,KAAK,KAAK,KAAK,mBACd,2BAAC,SAAG;4CAAC,OAAM;4CAAQ,OAAO;gDAAE,WAAW;4CAAE;sDAAG;;;;;;;;;;;;8CAMhD,2BAAC,UAAI;oCACH,MAAK;oCACL,YAAY;oCACZ,YAAY,CAAC,wBACX,2BAAC,UAAI,CAAC,IAAI;;8DACR,2BAAC,oBAAa;oDAAC,OAAO;wDAAE,OAAO;wDAAW,aAAa;oDAAE;;;;;;gDACxD;;;;;;;oCAGL,OAAO;wCAAE,cAAc;oCAAG;;;;;;8CAG5B,2BAAC,YAAM;oCACL,MAAM,YAAY,YAAY;oCAC9B,MAAK;oCACL,KAAK;oCACL,oBAAM,2BAAC,2BAAoB;;;;;oCAC3B,SAAS,IAAM,gBAAgB;oCAC/B,UAAU,KAAK,KAAK,KAAK;8CAExB,KAAK,KAAK,KAAK,IAAI,SAAS;;;;;;;;;;;;uBAtEF,KAAK,EAAE;;;;;gBA2E5C;;;;;;0BAIF,2BAAC,UAAI;gBAAC,OAAM;gBAAO,OAAO;oBAAE,WAAW;gBAAG;0BACxC,cAAA,2BAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAO;8BAC7B,MAAM,MAAM,GAAG,mBACd,2BAAC;kCACE,6BAAmB,CAAC,YAAY,CAAC,OAAO,GAAG,CAAC,CAAC,YAAY,sBACxD,2BAAC,SAAG;gCAA6B,OAAO;oCAAE,SAAS;oCAAU,cAAc;gCAAoB;;kDAC7F,2BAAC,SAAG;wCAAC,MAAM;kDACT,cAAA,2BAAC;4CAAK,MAAM;sDAAE,WAAW,OAAO;;;;;;;;;;;oCAEjC,WAAW,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,2BAC7B,2BAAC,SAAG;4CAAC,MAAM;sDACT,cAAA,2BAAC;0DACE,OAAO,UAAU,YAAa,QAAQ,MAAM,MAAO;;;;;;2CAFrC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,WAAW,CAAC;;;;;;+BAL3C,CAAC,WAAW,EAAE,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;0BAmBzC,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU,IAAM,yBAAyB;gBACzC,QAAQ;kCACN,2BAAC,YAAM;wBAAc,SAAS,IAAM,yBAAyB;kCAAQ;uBAAzD;;;;;kCAGZ,2BAAC,YAAM;wBAEL,MAAK;wBACL,SAAS;wBACT,SAAS;kCACV;uBAJK;;;;;iBAOP;0BAEA,8BACC,2BAAC;;sCACC,2BAAC;4BAAI,OAAO;gCAAE,cAAc;4BAAG;;8CAC7B,2BAAC;oCAAK,MAAM;8CAAC;;;;;;8CACb,2BAAC;8CAAM,aAAa,IAAI;;;;;;;;;;;;sCAG1B,2BAAC;4BAAI,OAAO;gCAAE,cAAc;4BAAG;;8CAC7B,2BAAC;oCAAK,MAAM;8CAAC;;;;;;8CACb,2BAAC,iBAAW;oCACV,KAAK;oCACL,KAAK;oCACL,OAAO;oCACP,UAAU,CAAC,QAAU,YAAY,SAAS;oCAC1C,YAAW;oCACX,OAAO;wCAAE,YAAY;oCAAE;;;;;;;;;;;;sCAI3B,2BAAC,aAAO;;;;;sCAER,2BAAC;;8CACC,2BAAC;oCAAK,MAAM;8CAAC;;;;;;gCACX,CAAA;oCACA,MAAM,YAAY,eAAe,cAAc;oCAC/C,qBACE,2BAAC;wCAAI,OAAO;4CAAE,WAAW;wCAAE;;0DACzB,2BAAC;;oDAAI;oDAAK,UAAU,aAAa;;;;;;;4CAChC,UAAU,QAAQ,GAAG,mBACpB,2BAAC;gDAAI,OAAO;oDAAE,OAAO;gDAAU;;oDAAG;oDAC3B,UAAU,QAAQ;oDAAC;;;;;;;0DAG5B,2BAAC;gDAAI,OAAO;oDAAE,UAAU;oDAAI,YAAY;oDAAQ,OAAO;gDAAU;;oDAAG;oDAC7D,UAAU,UAAU;;;;;;;;;;;;;gCAIjC,CAAA;;;;;;;;;;;;;;;;;;;;;;;;AAOd;GA5QM;KAAA;IA8QN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/Sf;;CAEC;;;;4BAiND;;;eAAA;;;;;;0DA/M2C;6BAWpC;8BAOA;4BACiB;iCACiB;;;;;;;;;;AAGzC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAClC,MAAM,EAAE,MAAM,EAAE,GAAG,WAAK;AAExB,MAAM,kBAA4B;;IAChC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC3E,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;IAE7C,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,IAAA,gBAAS,EAAC;QAER,MAAM,WAAW,MAAM,MAAM,CAAC,CAAA,OAC5B,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACtD,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAErF,iBAAiB;IACnB,GAAG;QAAC;QAAO;KAAW;IAEtB,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,qBAAW,CAAC,YAAY;YAC/C,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,YAAO,CAAC,IAAI,CAAC;IACf;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YAEF,MAAM,WAAW,MAAM,qBAAW,CAAC,SAAS,CAAC;gBAAE,QAAQ,KAAK,EAAE;YAAC;YAG/D,IAAI,SAAS,oBAAoB,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,EAAE,KAAK,KAAK,EAAE,EAChF,aAAO,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC;iBAEhC;gBACL,QAAQ,KAAK,CAAC;gBACd,aAAO,CAAC,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,qBAAW,CAAC,SAAS,CAAC;gBAAE,QAAQ,KAAK,EAAE;YAAC;YAG/D,IAAI,SAAS,oBAAoB,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,EAAE,KAAK,KAAK,EAAE,EAAE;gBAClF,aAAO,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC;gBAGrC,WAAW;oBACT,YAAO,CAAC,IAAI,CAAC;gBACf,GAAG;YACL,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,aAAO,CAAC,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,OACE,2BAAC;;YACC,2BAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAG;0BAC7B,2BAAC;oBACC,aAAY;oBACZ,UAAU;oBACV,OAAO;oBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oBAC7C,OAAO;wBAAE,OAAO;oBAAI;;;;;;;;;;;YAIvB,cAAc,MAAM,KAAK,KAAK,CAAC,UAC9B,2BAAC,WAAK;gBACJ,OAAO,WAAK,CAAC,sBAAsB;gBACnC,aACE,aAAa,cAAc;0BAG5B,CAAC,cACA,2BAAC,YAAM;oBAAC,MAAK;oBAAU,SAAS;8BAAkB;;;;;;;;;;uBAMtD,2BAAC,UAAI;gBACH,SAAS;gBACT,YAAW;gBACX,YAAY;gBACZ,YAAY;oBACV,iBAAiB;oBACjB,iBAAiB;oBACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;gBACxC;gBACA,YAAY,CAAC,OACX,2BAAC,UAAI,CAAC,IAAI;wBACR,SAAS;4BACP,2BAAC,YAAM;gCAEL,MAAK;gCACL,MAAM,2BAAC,kBAAW;;;;;gCAClB,SAAS,IAAM,eAAe;0CAC/B;+BAJK;;;;;4BAON,2BAAC,YAAM;gCAEL,MAAK;gCACL,MAAK;gCACL,SAAS,IAAM,iBAAiB;0CACjC;+BAJK;;;;;yBAOP;kCAED,2BAAC,UAAI,CAAC,IAAI,CAAC,IAAI;4BACb,QACE,2BAAC,YAAM;gCACL,MAAM;gCACN,MAAM,2BAAC,mBAAY;;;;;gCACnB,OAAO;oCAAE,iBAAiB;gCAAU;;;;;;4BAGxC,OACE,2BAAC,WAAK;;oCACJ,2BAAC;wCAAM,OAAO;wCAAG,OAAO;4CAAE,QAAQ;wCAAE;kDACjC,KAAK,IAAI;;;;;;oCAEX,KAAK,SAAS,IACb,2BAAC,SAAG;wCAAC,OAAM;wCAAO,MAAM,2BAAC,oBAAa;;;;;kDAAK;;;;;;;;;;;;4BAMjD,aACE,2BAAC,WAAK;gCAAC,WAAU;gCAAW,MAAK;;oCAC9B,KAAK,WAAW,IACf,2BAAC;wCAAK,MAAK;kDAAa,KAAK,WAAW;;;;;;oCAE1C,2BAAC,WAAK;;4CACJ,2BAAC,WAAK;gDAAC,MAAK;;oDACV,2BAAC,mBAAY;;;;;oDACb,2BAAC;wDAAK,MAAK;;4DAAa,KAAK,WAAW;4DAAC;;;;;;;;;;;;;4CAE3C,2BAAC;gDAAK,MAAK;;oDAAY;oDAChB,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;4CAElD,2BAAC;gDAAK,MAAK;;oDAAY;oDAChB,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxE;GAnLM;KAAA;IAqLN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnNf;;CAEC;;;;4BAgTD;;;eAAA;;;;;;wEA9SgC;6BAezB;8BAQA;iCACqB;;;;;;;;;;AAG5B,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAc3B,MAAM,oBAAsD,CAAC,EAC3D,OAAO,EACP,QAAQ,EACR,SAAS,EACV;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,eAAQ,EAAW;QAAC;KAAG;IACnD,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAiB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAAC;IAE/C,MAAM,iBAAiB;QACrB,IAAI,OAAO,MAAM,GAAG,IAClB,UAAU;eAAI;YAAQ;SAAG;aAEzB,aAAO,CAAC,OAAO,CAAC;IAEpB;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,MAAM,YAAY,OAAO,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAChD,UAAU;QACZ;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAe;QACxC,MAAM,YAAY;eAAI;SAAO;QAC7B,SAAS,CAAC,MAAM,GAAG;QACnB,UAAU;IACZ;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC;IACzB;IAEA,MAAM,eAAe;QACnB,aAAa;QACb,MAAM,cAAc,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,OAAO;QAE5D,IAAI,YAAY,MAAM,KAAK,GAAG;YAC5B,aAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,SAAS;QACT,MAAM,gBAAgB,YAAY,MAAM,CAAC,CAAA,QAAS,CAAC,cAAc;QACjE,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,aAAO,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,cAAc,IAAI,CAAC,MAAM,CAAC;YACtD;QACF;QAEA,SAAS;QACT,MAAM,eAAe;eAAI,IAAI,IAAI;SAAa;QAC9C,IAAI,aAAa,MAAM,KAAK,YAAY,MAAM,EAAE;YAC9C,aAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,IAAI;YACF,WAAW;YACX,eAAe;YAEf,MAAM,UAAgC;gBACpC,QAAQ;YACV;YAEA,MAAM,qBAAW,CAAC,aAAa,CAAC;YAEhC,oBAAoB;YACpB,MAAM,UAA0B,aAAa,GAAG,CAAC,CAAA,QAAU,CAAA;oBACzD;oBACA,SAAS;gBACX,CAAA;YAEA,iBAAiB;YACjB,eAAe;YAEf,aAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,aAAa,MAAM,CAAC,IAAI,CAAC;YAEjD,kBAAkB;YAClB,WAAW;gBACT;gBACA;YACF,GAAG;QAEL,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,WAAW;YAEzB,YAAY;YACZ,MAAM,UAA0B,aAAa,GAAG,CAAC,CAAA,QAAU,CAAA;oBACzD;oBACA,SAAS;oBACT,OAAO,MAAM,OAAO,IAAI;gBAC1B,CAAA;YAEA,iBAAiB;YACjB,eAAe;YACf,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,UAAU;YAAC;SAAG;QACd,iBAAiB,EAAE;QACnB,eAAe;QACf,KAAK,WAAW;QAChB;IACF;IAEA,qBACE,2BAAC,WAAK;QACJ,OAAM;QACN,MAAM;QACN,UAAU;QACV,QACE,cAAc;0BACZ,2BAAC,YAAM;gBAAa,MAAK;gBAAU,SAAS;0BAAc;eAA9C;;;;;SAGb,GAAG;0BACF,2BAAC,YAAM;gBAAc,SAAS;0BAAc;eAAhC;;;;;0BAGZ,2BAAC,YAAM;gBAEL,MAAK;gBACL,SAAS;gBACT,SAAS;gBACT,UAAU,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,OAAO,IAAI,MAAM,KAAK;0BAClE;eALK;;;;;SAQP;QAEH,OAAO;;YAEN,CAAC,4BACA;;kCACE,2BAAC;wBAAI,OAAO;4BAAE,cAAc;wBAAG;kCAC7B,cAAA,2BAAC;4BAAK,MAAK;sCAAY;;;;;;;;;;;kCAKzB,2BAAC,WAAK;wBACJ,SAAQ;wBACR,2BACE,2BAAC;4BAAG,OAAO;gCAAE,WAAW;gCAAG,aAAa;gCAAI,cAAc;4BAAE;;8CAC1D,2BAAC;8CAAG;;;;;;8CACJ,2BAAC;8CAAG;;;;;;8CACJ,2BAAC;8CAAG;;;;;;8CACJ,2BAAC;8CAAG;;;;;;;;;;;;wBAGR,MAAK;wBACL,QAAQ;wBACR,OAAO;4BAAE,cAAc;wBAAG;;;;;;;6CAI9B,2BAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAG;;kCAC7B,2BAAC,WAAK;wBACJ,SAAQ;wBACR,aAAa,CAAC,IAAI,EAAE,cAAc,MAAM,CAAC,IAAI,CAAC;wBAC9C,MAAM,cAAc,KAAK,CAAC,CAAA,IAAK,EAAE,OAAO,IAAI,YAAY;wBACxD,QAAQ;wBACR,OAAO;4BAAE,cAAc;wBAAG;;;;;;kCAG5B,2BAAC,UAAI;wBACH,MAAK;wBACL,YAAY;wBACZ,YAAY,CAAC,uBACX,2BAAC,UAAI,CAAC,IAAI;0CACR,cAAA,2BAAC,WAAK;;wCACH,OAAO,OAAO,iBACb,2BAAC,0BAAmB;4CAAC,OAAO;gDAAE,OAAO;4CAAU;;;;;mEAE/C,2BAAC,gCAAyB;4CAAC,OAAO;gDAAE,OAAO;4CAAU;;;;;;sDAEvD,2BAAC;sDAAM,OAAO,KAAK;;;;;;wCAClB,OAAO,OAAO,iBACb,2BAAC,SAAG;4CAAC,OAAM;sDAAU;;;;;mEAErB,2BAAC,aAAO;4CAAC,OAAO,OAAO,KAAK;sDAC1B,cAAA,2BAAC,SAAG;gDAAC,OAAM;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUpC,CAAC,6BACA,2BAAC,UAAI;gBAAC,MAAM;gBAAM,QAAO;;kCACvB,2BAAC,UAAI,CAAC,IAAI;wBAAC,OAAM;kCACf,cAAA,2BAAC,WAAK;4BAAC,WAAU;4BAAW,OAAO;gCAAE,OAAO;4BAAO;sCAChD,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,2BAAC,WAAK;oCAAa,OAAO;wCAAE,OAAO;oCAAO;;sDACxC,2BAAC,WAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,OAAO,EAAE,MAAM,CAAC,KAAK;4CACxD,sBAAQ,2BAAC,mBAAY;;;;;4CACrB,OAAO;gDAAE,MAAM;4CAAE;4CACjB,QAAQ,SAAS,CAAC,cAAc,SAAS,UAAU;;;;;;wCAEpD,OAAO,MAAM,GAAG,mBACf,2BAAC,YAAM;4CACL,MAAK;4CACL,MAAM;4CACN,oBAAM,2BAAC,qBAAc;;;;;4CACrB,SAAS,IAAM,kBAAkB;;;;;;;mCAd3B;;;;;;;;;;;;;;;oBAsBjB,OAAO,MAAM,GAAG,oBACf,2BAAC,YAAM;wBACL,MAAK;wBACL,oBAAM,2BAAC,mBAAY;;;;;wBACnB,SAAS;wBACT,OAAO;4BAAE,OAAO;4BAAQ,cAAc;wBAAG;kCAC1C;;;;;;oBAKF,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,OAAO,IAAI,MAAM,GAAG,mBACpD,2BAAC;wBAAI,OAAO;4BAAE,WAAW;wBAAG;;0CAC1B,2BAAC;gCAAK,MAAM;0CAAC;;;;;;0CACb,2BAAC;gCAAI,OAAO;oCAAE,WAAW;gCAAE;0CACxB,OACE,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,OAAO,IACjC,GAAG,CAAC,CAAC,OAAO,sBACX,2BAAC,SAAG;wCAEF,OAAO,cAAc,SAAS,SAAS;wCACvC,OAAO;4CAAE,cAAc;wCAAE;kDAExB;uCAJI;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAc3B;GAnQM;;QAOW,UAAI,CAAC;;;KAPhB;IAqQN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AClTf;;CAEC;;;;4BAiRD;;;eAAA;;;;;;wEA/Q2C;6BAepC;8BAMA;iCACqB;;;;;;;;;;AAG5B,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,YAAM;AAgBzB,MAAM,oBAAsD,CAAC,EAC3D,OAAO,EACP,QAAQ,EACR,SAAS,EACT,aAAa,EACd;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAqB,EAAE;IAC/E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAW,EAAE;IACnE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,eAAQ;IACpD,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAE3B,IAAA,gBAAS,EAAC;QACR,IAAI,SAAS;YACX;YACA;QACF;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,qBAAW,CAAC,cAAc,CAAC;gBAAE,SAAS;gBAAG,UAAU;YAAK;YAC/E,oBAAoB;YACpB,MAAM,oBAAoB,SAAS,IAAI,CAAC,MAAM,CAAC,CAAA,SAAU,CAAC,OAAO,SAAS;YAC1E,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI;YACF,kBAAkB;YAClB,MAAM,QAAQ,MAAM,qBAAW,CAAC,YAAY;YAC5C,MAAM,aAAa,MAChB,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,eAC3B,GAAG,CAAC,CAAA,OAAS,CAAA;oBACZ,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;oBACf,MAAM;oBACN,aAAa,KAAK,WAAW;gBAC/B,CAAA;YAEF,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,aAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,IAAI,CAAC,gBAAgB;YACnB,aAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,IAAI;YACF,WAAW;YAEX,oBAAoB;YACpB,2BAA2B;YAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,aAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,gBAAgB,MAAM,CAAC,WAAW,CAAC;YAC1D;YACA;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,mBAAmB,EAAE;QACrB,kBAAkB;QAClB,KAAK,WAAW;QAChB;IACF;IAEA,MAAM,wBAAwB,QAAQ,MAAM,CAAC,CAAA,SAC3C,gBAAgB,QAAQ,CAAC,OAAO,EAAE;IAGpC,MAAM,wBAAwB,iBAAiB,IAAI,CAAC,CAAA,SAClD,OAAO,EAAE,KAAK;IAGhB,qBACE,2BAAC,WAAK;QACJ,OAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;0BACN,2BAAC,YAAM;gBAAc,SAAS;0BAAc;eAAhC;;;;;0BAGZ,2BAAC,YAAM;gBAEL,MAAK;gBACL,SAAS;gBACT,SAAS;gBACT,UAAU,gBAAgB,MAAM,KAAK,KAAK,CAAC;0BAC5C;eALK;;;;;SAQP;QACD,OAAO;;0BAEP,2BAAC,WAAK;gBACJ,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,QAAQ;gBACR,OAAO;oBAAE,cAAc;gBAAG;;;;;;0BAG5B,2BAAC,UAAI;gBAAC,MAAM;gBAAM,QAAO;;kCACvB,2BAAC,UAAI,CAAC,IAAI;wBAAC,OAAM;kCACf,cAAA,2BAAC;4BAAI,OAAO;gCAAE,WAAW;gCAAK,WAAW;gCAAQ,QAAQ;gCAAqB,cAAc;gCAAG,SAAS;4BAAE;sCACvG,QAAQ,MAAM,KAAK,kBAClB,2BAAC;gCAAK,MAAK;0CAAY;;;;;qDAEvB,2BAAC,UAAI;gCACH,MAAK;gCACL,YAAY;gCACZ,YAAY,CAAC,uBACX,2BAAC,UAAI,CAAC,IAAI;wCACR,OAAO;4CACL,QAAQ;4CACR,iBAAiB,gBAAgB,QAAQ,CAAC,OAAO,EAAE,IAAI,YAAY;4CACnE,SAAS;4CACT,cAAc;4CACd,QAAQ;wCACV;wCACA,SAAS;4CACP,IAAI,gBAAgB,QAAQ,CAAC,OAAO,EAAE,GACpC,mBAAmB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,OAAO,EAAE;iDAE7D,mBAAmB,CAAA,OAAQ;uDAAI;oDAAM,OAAO,EAAE;iDAAC;wCAEnD;;0DAEA,2BAAC,UAAI,CAAC,IAAI,CAAC,IAAI;gDACb,sBAAQ,2BAAC,YAAM;oDAAC,MAAK;oDAAQ,oBAAM,2BAAC,mBAAY;;;;;;;;;;gDAChD,qBACE,2BAAC,WAAK;;wDACH,OAAO,IAAI;wDACX,gBAAgB,QAAQ,CAAC,OAAO,EAAE,mBACjC,2BAAC,0BAAmB;4DAAC,OAAO;gEAAE,OAAO;4DAAU;;;;;;;;;;;;gDAIrD,aAAa,OAAO,KAAK;;;;;;0DAE3B,2BAAC,SAAG;gDAAC,OAAO,OAAO,QAAQ,GAAG,UAAU;0DACrC,OAAO,QAAQ,GAAG,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASxC,2BAAC,UAAI,CAAC,IAAI;wBAAC,OAAM;kCACf,cAAA,2BAAC,YAAM;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU;4BACV,OAAO;gCAAE,OAAO;4BAAO;sCAEtB,iBAAiB,GAAG,CAAC,CAAA,uBACpB,2BAAC;oCAAuB,OAAO,OAAO,EAAE;8CACtC,cAAA,2BAAC,WAAK;;0DACJ,2BAAC,mBAAY;;;;;4CACZ,OAAO,IAAI;0DACZ,2BAAC;gDAAK,MAAK;;oDAAY;oDAAE,OAAO,WAAW;oDAAC;;;;;;;;;;;;;mCAJnC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;YAY7B,gBAAgB,MAAM,GAAG,KAAK,uCAC7B;;kCACE,2BAAC,aAAO;;;;;kCACR,2BAAC;;0CACC,2BAAC;gCAAK,MAAM;0CAAC;;;;;;0CACb,2BAAC;gCAAI,OAAO;oCAAE,WAAW;oCAAG,SAAS;oCAAI,YAAY;oCAAW,cAAc;gCAAE;0CAC9E,cAAA,2BAAC,WAAK;oCAAC,WAAU;oCAAW,OAAO;wCAAE,OAAO;oCAAO;;sDACjD,2BAAC;;8DACC,2BAAC;;wDAAK;wDAAK,sBAAsB,MAAM;wDAAC;;;;;;;8DACxC,2BAAC;oDAAI,OAAO;wDAAE,WAAW;oDAAE;8DACxB,sBAAsB,GAAG,CAAC,CAAA,uBACzB,2BAAC,SAAG;4DAAiB,OAAM;4DAAO,OAAO;gEAAE,QAAQ;4DAAM;sEACtD,OAAO,IAAI;2DADJ,OAAO,EAAE;;;;;;;;;;;;;;;;sDAMzB,2BAAC;4CAAI,OAAO;gDAAE,WAAW;4CAAS;sDAChC,cAAA,2BAAC,mBAAY;gDAAC,OAAO;oDAAE,UAAU;oDAAI,OAAO;gDAAU;;;;;;;;;;;sDAExD,2BAAC;;8DACC,2BAAC;8DAAK;;;;;;8DACN,2BAAC,SAAG;oDAAC,OAAM;oDAAQ,OAAO;wDAAE,YAAY;oDAAE;8DACvC,sBAAsB,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU/C;GAnOM;;QAWW,UAAI,CAAC;;;KAXhB;IAqON,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnRf;;CAEC;;;;4BA6OD;;;eAAA;;;;;;;wEA3OgC;6BAazB;8BAOA;iCACqB;gFAED;mFACG;mFACA;;;;;;;;;;AAE9B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;AAQ1B,MAAM,oBAAsD,CAAC,EAC3D,UAAU,EACV,OAAO,EACP,SAAS,EACV;;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IACzC,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAE3B,MAAM,aAAa;QACjB,IAAI,CAAC,YAAY;QACjB,KAAK,cAAc,CAAC;YAClB,MAAM,WAAW,IAAI;YACrB,aAAa,WAAW,WAAW;QACrC;QACA,oBAAoB;IACtB;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,YAAY;QAEjB,IAAI;YACF,YAAY;YACZ,MAAM,aAAgC;gBACpC,MAAM,OAAO,IAAI;gBACjB,aAAa,OAAO,WAAW;YACjC;YAEA,MAAM,qBAAW,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE;YAC5C,oBAAoB;YACpB,aAAO,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,YAAY;QACd;IACF;IAEA,IAAI,SACF,qBACE,2BAAC;QAAI,OAAO;YAAE,WAAW;YAAU,SAAS;QAAS;kBACnD,cAAA,2BAAC,UAAI;YAAC,MAAK;;;;;;;;;;;IAKjB,IAAI,CAAC,YACH,qBACE,2BAAC,WAAK;QACJ,OAAO,WAAK,CAAC,sBAAsB;QACnC,aAAY;;;;;;IAKlB,qBACE,2BAAC;;0BAEC,2BAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAG;;kCAC7B,2BAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,gBAAgB;4BAAiB,YAAY;4BAAU,cAAc;wBAAG;;0CACrG,2BAAC,WAAK;;kDACJ,2BAAC,mBAAY;wCAAC,OAAO;4CAAE,UAAU;4CAAI,OAAO;wCAAU;;;;;;kDACtD,2BAAC;wCAAM,OAAO;wCAAG,OAAO;4CAAE,QAAQ;wCAAE;kDACjC,WAAW,IAAI;;;;;;oCAEjB,WAAW,SAAS,kBACnB,2BAAC;wCAAK,MAAK;wCAAY,OAAO;4CAAE,UAAU;wCAAG;kDAAG;;;;;;;;;;;;4BAKnD,WAAW,SAAS,kBACnB,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCACL,MAAK;wCACL,oBAAM,2BAAC,sBAAe;;;;;wCACtB,SAAS,IAAM,sBAAsB;kDACtC;;;;;;kDAGD,2BAAC,YAAM;wCACL,oBAAM,2BAAC,mBAAY;;;;;wCACnB,SAAS,IAAM,sBAAsB;kDACtC;;;;;;kDAGD,2BAAC,YAAM;wCACL,oBAAM,2BAAC,mBAAY;;;;;wCACnB,SAAS;kDACV;;;;;;;;;;;;;;;;;;kCAOP,2BAAC,kBAAY;wBAAC,QAAQ;wBAAG,QAAQ;;0CAC/B,2BAAC,kBAAY,CAAC,IAAI;gCAAC,OAAM;0CACtB,WAAW,IAAI;;;;;;0CAElB,2BAAC,kBAAY,CAAC,IAAI;gCAAC,OAAM;;oCACtB,WAAW,WAAW;oCAAC;;;;;;;0CAE1B,2BAAC,kBAAY,CAAC,IAAI;gCAAC,OAAM;0CACtB,IAAI,KAAK,WAAW,SAAS,EAAE,cAAc;;;;;;0CAEhD,2BAAC,kBAAY,CAAC,IAAI;gCAAC,OAAM;0CACtB,IAAI,KAAK,WAAW,SAAS,EAAE,cAAc;;;;;;0CAEhD,2BAAC,kBAAY,CAAC,IAAI;gCAAC,OAAM;gCAAO,MAAM;0CACnC,WAAW,WAAW,IAAI;;;;;;;;;;;;;;;;;;0BAKjC,2BAAC,aAAO;;;;;0BAGR,2BAAC,uBAAc;gBAAC,QAAQ,WAAW,EAAE;gBAAE,WAAW,WAAW,SAAS;;;;;;0BAGtE,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU,IAAM,oBAAoB;gBACpC,QAAQ;0BAER,cAAA,2BAAC,UAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU;;sCAEV,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,UAAU;oCAAM,SAAS;gCAAU;gCACrC;oCAAE,KAAK;oCAAG,KAAK;oCAAI,SAAS;gCAAoB;6BACjD;sCAED,cAAA,2BAAC,WAAK;gCAAC,aAAY;;;;;;;;;;;sCAGrB,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,KAAK;oCAAK,SAAS;gCAAiB;6BACvC;sCAED,cAAA,2BAAC;gCACC,MAAM;gCACN,aAAY;gCACZ,SAAS;gCACT,WAAW;;;;;;;;;;;sCAIf,2BAAC,UAAI,CAAC,IAAI;4BAAC,OAAO;gCAAE,cAAc;gCAAG,WAAW;4BAAQ;sCACtD,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCAAC,SAAS,IAAM,oBAAoB;kDAAQ;;;;;;kDAGnD,2BAAC,YAAM;wCAAC,MAAK;wCAAU,UAAS;wCAAS,SAAS;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASpE,2BAAC,0BAAiB;gBAChB,SAAS;gBACT,UAAU,IAAM,sBAAsB;gBACtC,QAAQ,WAAW,EAAE;gBACrB,WAAW;oBACT,sBAAsB;oBACtB;gBACF;;;;;;0BAIF,2BAAC,0BAAiB;gBAChB,SAAS;gBACT,UAAU,IAAM,sBAAsB;gBACtC,QAAQ,WAAW,EAAE;gBACrB,WAAW;oBACT,sBAAsB;oBACtB;gBACF;;;;;;;;;;;;AAIR;GArMM;;QASW,UAAI,CAAC;;;KAThB;IAuMN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/Of;;CAEC;;;;4BAmWD;;;eAAA;;;;;;wEAjW2C;6BAkBpC;8BAYA;iCAGqB;;;;;;;;;;AAG5B,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,YAAM;AAQzB,MAAM,iBAAgD,CAAC,EACrD,MAAM,EACN,SAAS,EACT,cAAc,EACf;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC/E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAc,EAAE;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAS;IAEzD,IAAA,gBAAS,EAAC;QACR;IACF,GAAG;QAAC;KAAO;IAEX,IAAA,gBAAS,EAAC;QACR,kBAAkB;QAClB,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,UAAU;YACvC,mBAAmB,EAAE;YACrB;QACF;QAEA,MAAM,WAAW,QAAQ,MAAM,CAAC,CAAA;YAC9B,sBAAsB;YACtB,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,KAAK,EAC1C,OAAO;YAGT,MAAM,gBAAgB,CAAC,cACrB,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAE5D,MAAM,gBAAgB,iBAAiB,SACpC,iBAAiB,YAAY,OAAO,QAAQ,IAC5C,iBAAiB,cAAc,CAAC,OAAO,QAAQ,IAC/C,iBAAiB,aAAa,OAAO,SAAS,IAC9C,iBAAiB,YAAY,CAAC,OAAO,SAAS;YAEjD,OAAO,iBAAiB;QAC1B;QACA,mBAAmB;IACrB,GAAG;QAAC;QAAS;QAAY;KAAa;IAEtC,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,qBAAW,CAAC,cAAc,CAAC;gBAAE,SAAS;gBAAG,UAAU;YAAK;YAC/E,eAAe;YACf,WAAW,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,EAAE;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;YACd,YAAY;YACZ,WAAW,EAAE;QACf,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,OAAO,SAAS,EAAE;YACpB,aAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC;YACtC,QAAQ;YACR,YAAY;YACZ,MAAM;gBACJ,IAAI;oBACF,MAAM,qBAAW,CAAC,YAAY,CAAC,OAAO,EAAE;oBACxC,aAAO,CAAC,OAAO,CAAC;oBAChB;oBACA,2BAAA,6BAAA;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,WAAW;gBAC3B;YACF;QACF;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA,SACrC,gBAAgB,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,SAAS;QAG1D,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,aAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS,CAAC,SAAS,EAAE,gBAAgB,MAAM,CAAC,MAAM,CAAC;YACnD,QAAQ;YACR,YAAY;YACZ,MAAM;gBACJ,IAAI;oBACF,MAAM,QAAQ,GAAG,CACf,gBAAgB,GAAG,CAAC,CAAA,SAAU,qBAAW,CAAC,YAAY,CAAC,OAAO,EAAE;oBAElE,aAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,gBAAgB,MAAM,CAAC,IAAI,CAAC;oBACpD,mBAAmB,EAAE;oBACrB;oBACA,2BAAA,6BAAA;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,aAAa;oBAC3B,aAAO,CAAC,KAAK,CAAC;gBAChB;YACF;QACF;IACF;IAEA,MAAM,UAA2C;QAC/C;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,MAAM,uBACb,2BAAC,WAAK;;sCACJ,2BAAC,YAAM;4BAAC,MAAK;4BAAQ,oBAAM,2BAAC,mBAAY;;;;;;;;;;sCACxC,2BAAC;;8CACC,2BAAC;8CAAK;;;;;;8CACN,2BAAC;oCAAI,OAAO;wCAAE,UAAU;wCAAI,OAAO;oCAAO;8CAAI,OAAO,KAAK;;;;;;;;;;;;;;;;;;QAIlE;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,0BACP,2BAAC,SAAG;oBAAC,OAAO,YAAY,SAAS;oBAAQ,MAAM,0BAAY,2BAAC,oBAAa;;;;+CAAM,2BAAC,mBAAY;;;;;8BACzF,YAAY,QAAQ;;;;;;QAG3B;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,yBACP,2BAAC,SAAG;oBAAC,OAAO,WAAW,UAAU;8BAC9B,WAAW,OAAO;;;;;;QAGzB;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,aAAe,IAAI,KAAK,YAAY,kBAAkB;QACjE;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC;gBACP,MAAM,OAAO,IAAI,KAAK;gBACtB,MAAM,MAAM,IAAI;gBAChB,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,CAAA,IAAI,OAAO,KAAK,KAAK,OAAO,EAAC,IAAM;gBAEhE,IAAI,QAAQ;gBACZ,IAAI,WAAW,GAAG,QAAQ;gBAC1B,IAAI,WAAW,IAAI,QAAQ;gBAE3B,qBACE,2BAAC,aAAO;oBAAC,OAAO,KAAK,cAAc;8BACjC,cAAA,2BAAC,SAAG;wBAAC,OAAO;kCACT,aAAa,IAAI,OAAO,CAAC,EAAE,SAAS,EAAE,CAAC;;;;;;;;;;;YAIhD;QACF;QACA;YACE,OAAO;YACP,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG;gBACV,IAAI,CAAC,aAAa,OAAO,SAAS,EAChC,qBAAO,2BAAC;oBAAK,MAAK;8BAAY;;;;;;gBAGhC,MAAM,YAAgC;oBACpC;wBACE,KAAK;wBACL,OAAO;wBACP,oBAAM,2BAAC,qBAAc;;;;;wBACrB,QAAQ;wBACR,SAAS,IAAM,mBAAmB;oBACpC;iBACD;gBAED,qBACE,2BAAC,WAAK;oBAAC,MAAK;;sCACV,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAM;4BACN,MAAK;4BACL,oBAAM,2BAAC,qBAAc;;;;;4BACrB,SAAS,IAAM,mBAAmB;sCACnC;;;;;;sCAGD,2BAAC,cAAQ;4BAAC,MAAM;gCAAE,OAAO;4BAAU;4BAAG,SAAS;gCAAC;6BAAQ;sCACtD,cAAA,2BAAC,YAAM;gCACL,MAAK;gCACL,MAAK;gCACL,oBAAM,2BAAC,mBAAY;;;;;;;;;;;;;;;;;;;;;YAK7B;QACF;KACD;IAED,MAAM,eAAe;QACnB;QACA,UAAU,CAAC;YACT,mBAAmB;QACrB;QACA,kBAAkB,CAAC,SAAgC,CAAA;gBACjD,UAAU,OAAO,SAAS;YAC5B,CAAA;IACF;IAEA,qBACE,2BAAC,UAAI;QACH,qBACE,2BAAC,WAAK;;8BACJ,2BAAC;oBAAK,MAAM;8BAAC;;;;;;8BACb,2BAAC,WAAK;oBAAC,OAAO,gBAAgB,MAAM;oBAAE,QAAQ;;;;;;;;;;;;QAGlD,qBACE,2BAAC,WAAK;;8BACJ,2BAAC,YAAM;oBACL,OAAO;oBACP,UAAU;oBACV,OAAO;wBAAE,OAAO;oBAAI;oBACpB,MAAK;;sCAEL,2BAAC;4BAAO,OAAM;sCAAM;;;;;;sCACpB,2BAAC;4BAAO,OAAM;sCAAS;;;;;;sCACvB,2BAAC;4BAAO,OAAM;sCAAW;;;;;;sCACzB,2BAAC;4BAAO,OAAM;sCAAU;;;;;;sCACxB,2BAAC;4BAAO,OAAM;sCAAS;;;;;;;;;;;;8BAEzB,2BAAC,WAAK;oBACJ,aAAY;oBACZ,sBAAQ,2BAAC,qBAAc;;;;;oBACvB,OAAO;oBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oBAC7C,OAAO;wBAAE,OAAO;oBAAI;oBACpB,MAAK;;;;;;;;;;;;;YAKV,gBAAgB,MAAM,GAAG,KAAK,2BAC7B,2BAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAI,SAAS;oBAAI,YAAY;oBAAW,cAAc;gBAAE;0BAClF,cAAA,2BAAC,WAAK;;sCACJ,2BAAC;;gCAAK;gCAAK,gBAAgB,MAAM;gCAAC;;;;;;;sCAClC,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAM;4BACN,oBAAM,2BAAC,qBAAc;;;;;4BACrB,SAAS;sCACV;;;;;;sCAGD,2BAAC,YAAM;4BACL,MAAK;4BACL,SAAS,IAAM,mBAAmB,EAAE;sCACrC;;;;;;;;;;;;;;;;;0BAOP,2BAAC,WAAK;gBACJ,SAAS;gBACT,YAAY;gBACZ,QAAO;gBACP,SAAS;gBACT,cAAc,YAAY,eAAe;gBACzC,YAAY;oBACV,iBAAiB;oBACjB,iBAAiB;oBACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;oBACtC,UAAU;gBACZ;;;;;;;;;;;;AAIR;GAlTM;KAAA;IAoTN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrWf;;CAEC;;;;4BAyTD;;;eAAA;;;;;;wEAvT2C;6BAYpC;8BAQA;iCACqB;;;;;;;;;;AAG5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAElC,MAAM,qBAA+B;;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,eAAQ,EAAC;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAA6B;IAC3E,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,IAAA,eAAQ,EAAC;IACjE,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAC3B,MAAM,CAAC,aAAa,GAAG,UAAI,CAAC,OAAO;IAEnC,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI;YACF,WAAW;YACX,MAAM,UAAU,MAAM,qBAAW,CAAC,cAAc;YAChD,eAAe;YACf,KAAK,cAAc,CAAC;gBAClB,UAAU,QAAQ,QAAQ;gBAC1B,OAAO,QAAQ,KAAK;gBACpB,OAAO,QAAQ,KAAK;gBACpB,UAAU,QAAQ,QAAQ;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,UAAU;YACV,MAAM,aAAuC;gBAC3C,OAAO,OAAO,KAAK;gBACnB,OAAO,OAAO,KAAK;gBACnB,UAAU,OAAO,QAAQ;YAC3B;YAEA,MAAM,iBAAiB,MAAM,qBAAW,CAAC,iBAAiB,CAAC;YAC3D,eAAe;YACf,WAAW;YACX,aAAO,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,IAAI;YACF,MAAM,qBAAW,CAAC,cAAc,CAAC;gBAC/B,aAAa,OAAO,WAAW;gBAC/B,aAAa,OAAO,WAAW;YACjC;YACA,wBAAwB;YACxB,aAAa,WAAW;YACxB,aAAO,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,eAAe;QACnB,WAAW;QACX,IAAI,aACF,KAAK,cAAc,CAAC;YAClB,UAAU,YAAY,QAAQ;YAC9B,OAAO,YAAY,KAAK;YACxB,OAAO,YAAY,KAAK;YACxB,UAAU,YAAY,QAAQ;QAChC;IAEJ;IAEA,IAAI,WAAW,CAAC,aACd,qBAAO,2BAAC;kBAAI;;;;;;IAGd,qBACE,2BAAC;;0BACC,2BAAC;gBAAI,OAAO;oBAAE,SAAS;oBAAQ,YAAY;oBAAc,KAAK;gBAAG;;kCAE/D,2BAAC;wBAAI,OAAO;4BAAE,WAAW;wBAAS;;0CAChC,2BAAC,YAAM;gCAAC,MAAM;gCAAK,oBAAM,2BAAC,mBAAY;;;;;;;;;;0CACtC,2BAAC;gCAAI,OAAO;oCAAE,WAAW;gCAAG;0CAC1B,cAAA,2BAAC,YAAM;oCACL,gBAAgB;oCAChB,cAAc;wCACZ,aAAO,CAAC,IAAI,CAAC;wCACb,OAAO;oCACT;8CAEA,cAAA,2BAAC,YAAM;wCAAC,oBAAM,2BAAC,qBAAc;;;;;wCAAK,MAAK;kDAAQ;;;;;;;;;;;;;;;;;;;;;;kCAQrD,2BAAC;wBAAI,OAAO;4BAAE,MAAM;wBAAE;;0CACpB,2BAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,gBAAgB;oCAAiB,YAAY;oCAAU,cAAc;gCAAG;;kDACrG,2BAAC;wCAAM,OAAO;wCAAG,OAAO;4CAAE,QAAQ;wCAAE;;0DAClC,2BAAC,mBAAY;;;;;4CAAG;;;;;;;oCAEjB,CAAC,yBACA,2BAAC,YAAM;wCACL,MAAK;wCACL,oBAAM,2BAAC,mBAAY;;;;;wCACnB,SAAS,IAAM,WAAW;kDAC3B;;;;;;;;;;;;0CAML,2BAAC,UAAI;gCACH,MAAM;gCACN,QAAO;gCACP,UAAU;gCACV,UAAU,CAAC;;kDAEX,2BAAC,UAAI,CAAC,IAAI;wCACR,OAAM;wCACN,MAAK;kDAEL,cAAA,2BAAC,WAAK;4CACJ,sBAAQ,2BAAC,mBAAY;;;;;4CACrB,QAAQ;4CACR,aAAY;;;;;;;;;;;kDAIhB,2BAAC,UAAI,CAAC,IAAI;wCACR,OAAM;wCACN,MAAK;wCACL,OAAO;4CACL;gDAAE,UAAU;gDAAM,SAAS;4CAAU;4CACrC;gDAAE,MAAM;gDAAS,SAAS;4CAAa;yCACxC;kDAED,cAAA,2BAAC,WAAK;4CACJ,sBAAQ,2BAAC,mBAAY;;;;;4CACrB,aAAY;;;;;;;;;;;kDAIhB,2BAAC,UAAI,CAAC,IAAI;wCACR,OAAM;wCACN,MAAK;wCACL,OAAO;4CACL;gDAAE,SAAS;gDAAiB,SAAS;4CAAa;yCACnD;kDAED,cAAA,2BAAC,WAAK;4CAAC,aAAY;;;;;;;;;;;kDAGrB,2BAAC,UAAI,CAAC,IAAI;wCACR,OAAM;wCACN,MAAK;wCACL,OAAO;4CACL;gDAAE,KAAK;gDAAI,SAAS;4CAAgB;yCACrC;kDAED,cAAA,2BAAC,WAAK;4CAAC,aAAY;;;;;;;;;;;oCAGpB,yBACC,2BAAC,UAAI,CAAC,IAAI;kDACR,cAAA,2BAAC,WAAK;;8DACJ,2BAAC,YAAM;oDACL,MAAK;oDACL,UAAS;oDACT,SAAS;oDACT,oBAAM,2BAAC,mBAAY;;;;;8DACpB;;;;;;8DAGD,2BAAC,YAAM;oDAAC,SAAS;8DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU3C,2BAAC,aAAO;;;;;0BAGR,2BAAC;;kCACC,2BAAC;wBAAM,OAAO;wBAAG,OAAO;4BAAE,cAAc;wBAAG;;0CACzC,2BAAC,mBAAY;;;;;4BAAG;;;;;;;kCAElB,2BAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,gBAAgB;4BAAiB,YAAY;4BAAU,SAAS;wBAAS;;0CACtG,2BAAC;;kDACC,2BAAC;wCAAK,MAAM;kDAAC;;;;;;kDACb,2BAAC;;;;;kDACD,2BAAC;wCAAK,MAAK;kDAAY;;;;;;;;;;;;0CAEzB,2BAAC,YAAM;gCACL,SAAS,IAAM,wBAAwB;0CACxC;;;;;;;;;;;;;;;;;;0BAOL,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU;oBACR,wBAAwB;oBACxB,aAAa,WAAW;gBAC1B;gBACA,QAAQ;0BAER,cAAA,2BAAC,UAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU;;sCAEV,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,2BAAC,WAAK,CAAC,QAAQ;gCAAC,aAAY;;;;;;;;;;;sCAG9B,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,UAAU;oCAAM,SAAS;gCAAS;gCACpC;oCAAE,KAAK;oCAAG,SAAS;gCAAW;6BAC/B;sCAED,cAAA,2BAAC,WAAK,CAAC,QAAQ;gCAAC,aAAY;;;;;;;;;;;sCAG9B,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,cAAc;gCAAC;6BAAc;4BAC7B,OAAO;gCACL;oCAAE,UAAU;oCAAM,SAAS;gCAAS;gCACpC,CAAC,EAAE,aAAa,EAAE,GAAM,CAAA;wCACtB,WAAU,CAAC,EAAE,KAAK;4CAChB,IAAI,CAAC,SAAS,cAAc,mBAAmB,OAC7C,OAAO,QAAQ,OAAO;4CAExB,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;wCAClC;oCACF,CAAA;6BACD;sCAED,cAAA,2BAAC,WAAK,CAAC,QAAQ;gCAAC,aAAY;;;;;;;;;;;sCAG9B,2BAAC,UAAI,CAAC,IAAI;4BAAC,OAAO;gCAAE,cAAc;gCAAG,WAAW;4BAAQ;sCACtD,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCAAC,SAAS;4CACf,wBAAwB;4CACxB,aAAa,WAAW;wCAC1B;kDAAG;;;;;;kDAGH,2BAAC,YAAM;wCAAC,MAAK;wCAAU,UAAS;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvD;GA3RM;;QAMW,UAAI,CAAC;QACG,UAAI,CAAC;;;KAPxB;IA6RN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3Tf;;CAEC;;;;4BAyPD;;;eAAA;;;;;;wEAvP2C;6BAapC;8BAMA;iCACqB;;;;;;;;;;AAE5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAClC,MAAM,EAAE,MAAM,EAAE,GAAG,YAAM;AAczB,MAAM,sBAAgC;;IACpC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,eAAQ,EAAC;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAAyB;IACvE,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAE3B,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,uBAAuB;QAC3B,IAAI;YACF,WAAW;YACX,MAAM,QAAQ,MAAM,qBAAW,CAAC,kBAAkB;YAClD,eAAe;YACf,KAAK,cAAc,CAAC;gBAClB,UAAU,MAAM,QAAQ;gBACxB,UAAU,MAAM,QAAQ;gBACxB,OAAO,MAAM,KAAK;gBAClB,oBAAoB,MAAM,aAAa,CAAC,KAAK;gBAC7C,mBAAmB,MAAM,aAAa,CAAC,IAAI;gBAC3C,yBAAyB,MAAM,aAAa,CAAC,WAAW;gBACxD,2BAA2B,MAAM,aAAa,CAAC,aAAa;YAC9D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,UAAU;YACV,MAAM,aAAa;gBACjB,UAAU,OAAO,QAAQ;gBACzB,UAAU,OAAO,QAAQ;gBACzB,OAAO,OAAO,KAAK;gBACnB,eAAe;oBACb,OAAO,OAAO,kBAAkB;oBAChC,MAAM,OAAO,iBAAiB;oBAC9B,aAAa,OAAO,uBAAuB;oBAC3C,eAAe,OAAO,yBAAyB;gBACjD;YACF;YAEA,MAAM,qBAAW,CAAC,qBAAqB,CAAC;YACxC,eAAe;gBAAE,GAAG,WAAW;gBAAG,GAAG,UAAU;YAAC;YAChD,aAAO,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,UAAU;QACZ;IACF;IAEA,IAAI,WAAW,CAAC,aACd,qBAAO,2BAAC;kBAAI;;;;;;IAGd,qBACE,2BAAC,UAAI;QACH,MAAM;QACN,QAAO;QACP,UAAU;QACV,eAAe;YACb,UAAU,YAAY,QAAQ;YAC9B,UAAU,YAAY,QAAQ;YAC9B,OAAO,YAAY,KAAK;YACxB,oBAAoB,YAAY,aAAa,CAAC,KAAK;YACnD,mBAAmB,YAAY,aAAa,CAAC,IAAI;YACjD,yBAAyB,YAAY,aAAa,CAAC,WAAW;YAC9D,2BAA2B,YAAY,aAAa,CAAC,aAAa;QACpE;;0BAGA,2BAAC,UAAI;gBAAC,qBAAO;;sCAAE,2BAAC,qBAAc;;;;;wBAAG;;;gBAAU,OAAO;oBAAE,cAAc;gBAAG;;kCACnE,2BAAC,UAAI,CAAC,IAAI;wBACR,OAAM;wBACN,MAAK;wBACL,SAAQ;kCAER,cAAA,2BAAC,YAAM;4BAAC,aAAY;;8CAClB,2BAAC;oCAAO,OAAM;8CAAQ;;;;;;8CACtB,2BAAC;oCAAO,OAAM;8CAAQ;;;;;;8CACtB,2BAAC;oCAAO,OAAM;8CAAQ;;;;;;;;;;;;;;;;;kCAI1B,2BAAC,UAAI,CAAC,IAAI;wBACR,OAAM;wBACN,MAAK;wBACL,SAAQ;kCAER,cAAA,2BAAC,YAAM;4BAAC,aAAY;;8CAClB,2BAAC;oCAAO,OAAM;8CAAgB;;;;;;8CAC9B,2BAAC;oCAAO,OAAM;8CAAmB;;;;;;8CACjC,2BAAC;oCAAO,OAAM;8CAAgB;;;;;;8CAC9B,2BAAC;oCAAO,OAAM;8CAAa;;;;;;;;;;;;;;;;;;;;;;;0BAMjC,2BAAC,UAAI;gBAAC,qBAAO;;sCAAE,2BAAC,kBAAW;;;;;wBAAG;;;gBAAU,OAAO;oBAAE,cAAc;gBAAG;;kCAChE,2BAAC,UAAI,CAAC,IAAI;wBACR,OAAM;wBACN,MAAK;wBACL,SAAQ;kCAER,cAAA,2BAAC,WAAK,CAAC,KAAK;;8CACV,2BAAC,WAAK;oCAAC,OAAM;8CAAQ;;;;;;8CACrB,2BAAC,WAAK;oCAAC,OAAM;8CAAO;;;;;;8CACpB,2BAAC,WAAK;oCAAC,OAAM;8CAAO;;;;;;;;;;;;;;;;;kCAIxB,2BAAC,WAAK;wBACJ,SAAQ;wBACR,aAAY;wBACZ,MAAK;wBACL,QAAQ;wBACR,OAAO;4BAAE,WAAW;wBAAG;;;;;;;;;;;;0BAK3B,2BAAC,UAAI;gBAAC,qBAAO;;sCAAE,2BAAC,mBAAY;;;;;wBAAG;;;gBAAU,OAAO;oBAAE,cAAc;gBAAG;0BACjE,cAAA,2BAAC,WAAK;oBAAC,WAAU;oBAAW,OAAO;wBAAE,OAAO;oBAAO;;sCACjD,2BAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,gBAAgB;gCAAiB,YAAY;4BAAS;;8CACnF,2BAAC;;sDACC,2BAAC;4CAAK,MAAM;sDAAC;;;;;;sDACb,2BAAC;;;;;sDACD,2BAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;8CAEzB,2BAAC,UAAI,CAAC,IAAI;oCAAC,MAAK;oCAAqB,eAAc;oCAAU,OAAO;wCAAE,QAAQ;oCAAE;8CAC9E,cAAA,2BAAC,YAAM;;;;;;;;;;;;;;;;sCAIX,2BAAC,aAAO;4BAAC,OAAO;gCAAE,QAAQ;4BAAS;;;;;;sCAEnC,2BAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,gBAAgB;gCAAiB,YAAY;4BAAS;;8CACnF,2BAAC;;sDACC,2BAAC;4CAAK,MAAM;sDAAC;;;;;;sDACb,2BAAC;;;;;sDACD,2BAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;8CAEzB,2BAAC,UAAI,CAAC,IAAI;oCAAC,MAAK;oCAAoB,eAAc;oCAAU,OAAO;wCAAE,QAAQ;oCAAE;8CAC7E,cAAA,2BAAC,YAAM;;;;;;;;;;;;;;;;sCAIX,2BAAC,aAAO;4BAAC,OAAO;gCAAE,QAAQ;4BAAS;;;;;;sCAEnC,2BAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,gBAAgB;gCAAiB,YAAY;4BAAS;;8CACnF,2BAAC;;sDACC,2BAAC;4CAAK,MAAM;sDAAC;;;;;;sDACb,2BAAC;;;;;sDACD,2BAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;8CAEzB,2BAAC,UAAI,CAAC,IAAI;oCAAC,MAAK;oCAA0B,eAAc;oCAAU,OAAO;wCAAE,QAAQ;oCAAE;8CACnF,cAAA,2BAAC,YAAM;;;;;;;;;;;;;;;;sCAIX,2BAAC,aAAO;4BAAC,OAAO;gCAAE,QAAQ;4BAAS;;;;;;sCAEnC,2BAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,gBAAgB;gCAAiB,YAAY;4BAAS;;8CACnF,2BAAC;;sDACC,2BAAC;4CAAK,MAAM;sDAAC;;;;;;sDACb,2BAAC;;;;;sDACD,2BAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;8CAEzB,2BAAC,UAAI,CAAC,IAAI;oCAAC,MAAK;oCAA4B,eAAc;oCAAU,OAAO;wCAAE,QAAQ;oCAAE;8CACrF,cAAA,2BAAC,YAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOf,2BAAC,UAAI;0BACH,cAAA,2BAAC,UAAI,CAAC,IAAI;8BACR,cAAA,2BAAC,WAAK;;0CACJ,2BAAC,YAAM;gCACL,MAAK;gCACL,UAAS;gCACT,SAAS;gCACT,oBAAM,2BAAC,mBAAY;;;;;gCACnB,MAAK;0CACN;;;;;;0CAGD,2BAAC,YAAM;gCACL,SAAS;oCACP,KAAK,WAAW;oCAChB,aAAO,CAAC,IAAI,CAAC;gCACf;0CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAhNM;;QAIW,UAAI,CAAC;;;KAJhB;IAkNN,WAAe"}