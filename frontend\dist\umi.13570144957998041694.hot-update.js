globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/components/TeamSwitcher/index.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _antdstyle = __mako_require__("node_modules/antd-style/es/index.js");
            var _services = __mako_require__("src/services/index.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Text } = _antd.Typography;
            const useStyles = (0, _antdstyle.createStyles)(({ token })=>({
                    teamSwitcher: {
                        display: 'flex',
                        alignItems: 'center',
                        padding: '4px 12px',
                        borderRadius: token.borderRadius,
                        backgroundColor: 'rgba(255, 255, 255, 0.1)',
                        border: '1px solid rgba(255, 255, 255, 0.2)',
                        cursor: 'pointer',
                        transition: 'all 0.3s',
                        '&:hover': {
                            backgroundColor: 'rgba(255, 255, 255, 0.15)',
                            borderColor: 'rgba(255, 255, 255, 0.3)'
                        }
                    },
                    teamInfo: {
                        display: 'flex',
                        alignItems: 'center',
                        gap: 8
                    },
                    teamName: {
                        color: 'rgba(255, 255, 255, 0.85)',
                        fontWeight: 500,
                        maxWidth: 150,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                    },
                    dropdownItem: {
                        display: 'flex',
                        alignItems: 'center',
                        gap: 8,
                        padding: '8px 12px',
                        minWidth: 200
                    },
                    currentTeamItem: {
                        backgroundColor: token.colorPrimaryBg
                    }
                }));
            const TeamSwitcher = ({ style })=>{
                _s();
                const [teams, setTeams] = (0, _react.useState)([]);
                const [loading, setLoading] = (0, _react.useState)(false);
                const [dropdownVisible, setDropdownVisible] = (0, _react.useState)(false);
                const { styles } = useStyles();
                const { initialState, setInitialState } = (0, _max.useModel)('@@initialState');
                const currentTeam = initialState === null || initialState === void 0 ? void 0 : initialState.currentTeam;
                // 获取用户的所有团队
                const fetchTeams = async ()=>{
                    try {
                        setLoading(true);
                        const teamList = await _services.TeamService.getUserTeams();
                        setTeams(teamList);
                    } catch (error) {
                        console.error('获取团队列表失败:', error);
                    } finally{
                        setLoading(false);
                    }
                };
                (0, _react.useEffect)(()=>{
                    if (dropdownVisible) fetchTeams();
                }, [
                    dropdownVisible
                ]);
                // 切换团队
                const handleTeamSwitch = async (teamId)=>{
                    if (teamId === (currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id)) {
                        setDropdownVisible(false);
                        return;
                    }
                    try {
                        setLoading(true);
                        const response = await _services.AuthService.selectTeam({
                            teamId
                        });
                        // 检查后端返回的团队选择成功标识
                        if (response.teamSelectionSuccess && response.team && response.team.id === teamId) {
                            _antd.message.success('团队切换成功！');
                            // 刷新 initialState 以更新团队信息
                            if ((initialState === null || initialState === void 0 ? void 0 : initialState.fetchTeamInfo) && (initialState === null || initialState === void 0 ? void 0 : initialState.fetchUserInfo) && setInitialState) try {
                                const [currentUser, currentTeam] = await Promise.all([
                                    initialState.fetchUserInfo(),
                                    initialState.fetchTeamInfo()
                                ]);
                                // 确保团队信息已正确获取
                                if (currentTeam && currentTeam.id === teamId) await setInitialState({
                                    ...initialState,
                                    currentUser,
                                    currentTeam
                                });
                                else {
                                    console.error('获取的团队信息与选择的团队不匹配');
                                    _antd.message.error('团队切换失败，请重试');
                                }
                            } catch (error) {
                                console.error('更新 initialState 失败:', error);
                                _antd.message.error('团队切换失败，请重试');
                            }
                            setDropdownVisible(false);
                        } else {
                            console.error('团队切换响应异常，未返回正确的团队信息');
                            _antd.message.error('团队切换失败，请重试');
                        }
                    } catch (error) {
                        console.error('团队切换失败:', error);
                        _antd.message.error('团队切换失败');
                    } finally{
                        setLoading(false);
                    }
                };
                // 跳转到团队管理页面
                const handleManageTeams = ()=>{
                    setDropdownVisible(false);
                    _max.history.push('/team/list');
                };
                // 跳转到团队选择页面
                const handleSelectTeam = ()=>{
                    setDropdownVisible(false);
                    _max.history.push('/user/team-select');
                };
                if (!currentTeam) return null;
                const dropdownItems = [
                    // 当前团队
                    {
                        key: 'current',
                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            className: `${styles.dropdownItem} ${styles.currentTeamItem}`,
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                    size: "small",
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                        fileName: "src/components/TeamSwitcher/index.tsx",
                                        lineNumber: 163,
                                        columnNumber: 38
                                    }, void 0)
                                }, void 0, false, {
                                    fileName: "src/components/TeamSwitcher/index.tsx",
                                    lineNumber: 163,
                                    columnNumber: 11
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                fontWeight: 500
                                            },
                                            children: currentTeam.name
                                        }, void 0, false, {
                                            fileName: "src/components/TeamSwitcher/index.tsx",
                                            lineNumber: 165,
                                            columnNumber: 13
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            type: "secondary",
                                            style: {
                                                fontSize: 12
                                            },
                                            children: "当前团队"
                                        }, void 0, false, {
                                            fileName: "src/components/TeamSwitcher/index.tsx",
                                            lineNumber: 166,
                                            columnNumber: 13
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/components/TeamSwitcher/index.tsx",
                                    lineNumber: 164,
                                    columnNumber: 11
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/components/TeamSwitcher/index.tsx",
                            lineNumber: 162,
                            columnNumber: 9
                        }, this),
                        disabled: true
                    },
                    {
                        type: 'divider'
                    },
                    // 其他团队
                    ...teams.filter((team)=>team.id !== currentTeam.id).map((team)=>({
                            key: team.id.toString(),
                            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                className: styles.dropdownItem,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                        size: "small",
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                            fileName: "src/components/TeamSwitcher/index.tsx",
                                            lineNumber: 182,
                                            columnNumber: 40
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/components/TeamSwitcher/index.tsx",
                                        lineNumber: 182,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                children: team.name
                                            }, void 0, false, {
                                                fileName: "src/components/TeamSwitcher/index.tsx",
                                                lineNumber: 184,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                style: {
                                                    fontSize: 12
                                                },
                                                children: [
                                                    team.memberCount,
                                                    " 名成员"
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/components/TeamSwitcher/index.tsx",
                                                lineNumber: 185,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/components/TeamSwitcher/index.tsx",
                                        lineNumber: 183,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/components/TeamSwitcher/index.tsx",
                                lineNumber: 181,
                                columnNumber: 11
                            }, this),
                            onClick: ()=>handleTeamSwitch(team.id)
                        })),
                    {
                        type: 'divider'
                    },
                    // 管理选项
                    {
                        key: 'select',
                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            className: styles.dropdownItem,
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SwapOutlined, {}, void 0, false, {
                                    fileName: "src/components/TeamSwitcher/index.tsx",
                                    lineNumber: 201,
                                    columnNumber: 11
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                    children: "选择团队"
                                }, void 0, false, {
                                    fileName: "src/components/TeamSwitcher/index.tsx",
                                    lineNumber: 202,
                                    columnNumber: 11
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/components/TeamSwitcher/index.tsx",
                            lineNumber: 200,
                            columnNumber: 9
                        }, this),
                        onClick: handleSelectTeam
                    },
                    {
                        key: 'manage',
                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            className: styles.dropdownItem,
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                    fileName: "src/components/TeamSwitcher/index.tsx",
                                    lineNumber: 211,
                                    columnNumber: 11
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                    children: "管理团队"
                                }, void 0, false, {
                                    fileName: "src/components/TeamSwitcher/index.tsx",
                                    lineNumber: 212,
                                    columnNumber: 11
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/components/TeamSwitcher/index.tsx",
                            lineNumber: 210,
                            columnNumber: 9
                        }, this),
                        onClick: handleManageTeams
                    }
                ];
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                    menu: {
                        items: dropdownItems
                    },
                    trigger: [
                        'click'
                    ],
                    open: dropdownVisible,
                    onOpenChange: setDropdownVisible,
                    placement: "bottomRight",
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        className: styles.teamSwitcher,
                        style: style,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                            spinning: loading,
                            size: "small",
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                className: styles.teamInfo,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                        size: "small",
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                            fileName: "src/components/TeamSwitcher/index.tsx",
                                            lineNumber: 230,
                                            columnNumber: 40
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/components/TeamSwitcher/index.tsx",
                                        lineNumber: 230,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        className: styles.teamName,
                                        children: currentTeam.name
                                    }, void 0, false, {
                                        fileName: "src/components/TeamSwitcher/index.tsx",
                                        lineNumber: 231,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DownOutlined, {
                                        style: {
                                            color: 'rgba(255, 255, 255, 0.65)',
                                            fontSize: 12
                                        }
                                    }, void 0, false, {
                                        fileName: "src/components/TeamSwitcher/index.tsx",
                                        lineNumber: 232,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/components/TeamSwitcher/index.tsx",
                                lineNumber: 229,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: "src/components/TeamSwitcher/index.tsx",
                            lineNumber: 228,
                            columnNumber: 9
                        }, this)
                    }, void 0, false, {
                        fileName: "src/components/TeamSwitcher/index.tsx",
                        lineNumber: 227,
                        columnNumber: 7
                    }, this)
                }, void 0, false, {
                    fileName: "src/components/TeamSwitcher/index.tsx",
                    lineNumber: 220,
                    columnNumber: 5
                }, this);
            };
            _s(TeamSwitcher, "VjD3qPhS8b7MhsvXBndc7k7wXQg=", false, function() {
                return [
                    useStyles,
                    _max.useModel
                ];
            });
            _c = TeamSwitcher;
            var _default = TeamSwitcher;
            var _c;
            $RefreshReg$(_c, "TeamSwitcher");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '5540011554859289253';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "common",
            "p__subscription__index"
        ],
        "src/pages/team/create/index.tsx": [
            "p__team__create__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "common",
            "p__user__index"
        ],
        "src/pages/user/login/index.tsx": [
            "p__user__login__index"
        ],
        "src/pages/user/team-select/index.tsx": [
            "src/pages/user/team-select/index.tsx"
        ]
    });
    ;
});

//# sourceMappingURL=umi.13570144957998041694.hot-update.js.map