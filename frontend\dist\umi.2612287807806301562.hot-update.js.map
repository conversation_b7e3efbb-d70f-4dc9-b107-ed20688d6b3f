{"version": 3, "sources": ["umi.2612287807806301562.hot-update.js", "config/defaultSettings.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='15187737457918140960';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/create/index.tsx\":[\"p__team__create__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "import type { ProLayoutProps } from '@ant-design/pro-components';\r\n\r\nconst Settings: ProLayoutProps & {\r\n  pwa?: boolean;\r\n  logo?: string;\r\n} = {\r\n  navTheme: 'light',\r\n  colorPrimary: '#1890ff',\r\n  layout: 'side',\r\n  contentWidth: 'Fluid',\r\n  fixedHeader: false,\r\n  fixSiderbar: true,\r\n  colorWeak: false,\r\n  title: '团队协作管理系统',\r\n  pwa: false,\r\n  logo: '/logo.svg',\r\n  iconfontUrl: '',\r\n  token: {},\r\n};\r\n\r\nexport default Settings;\r\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;wCCiBb;;;2BAAA;;;;;;;;;;;;;YAlBA,MAAM,WAGF;gBACF,UAAU;gBACV,cAAc;gBACd,QAAQ;gBACR,cAAc;gBACd,aAAa;gBACb,aAAa;gBACb,WAAW;gBACX,OAAO;gBACP,KAAK;gBACL,MAAM;gBACN,aAAa;gBACb,OAAO,CAAC;YACV;gBAEA,WAAe;;;;;;;;;;;;;;;;;;;;;;;IDjBD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;QAAC,wCAAuC;YAAC;SAAuC;IAAA;;AAC9yB"}