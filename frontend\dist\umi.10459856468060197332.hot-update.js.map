{"version": 3, "sources": ["umi.10459856468060197332.hot-update.js", "src/app.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='12583940200318496981';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/create/index.tsx\":[\"p__team__create__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "\r\nimport type { Settings as LayoutSettings } from '@ant-design/pro-components';\r\nimport { SettingDrawer } from '@ant-design/pro-components';\r\nimport type { RequestConfig, RunTimeLayoutConfig } from '@umijs/max';\r\nimport { history } from '@umijs/max';\r\nimport {\r\n  AvatarDropdown,\r\n  AvatarName,\r\n  Footer,\r\n  Question,\r\n  ErrorBoundary,\r\n  GlobalLoading,\r\n} from '@/components';\r\nimport { AuthService, UserService, TeamService } from '@/services';\r\nimport type { UserProfileResponse, TeamDetailResponse } from '@/types/api';\r\nimport defaultSettings from '../config/defaultSettings';\r\nimport { errorConfig } from './requestErrorConfig';\r\nimport '@ant-design/v5-patch-for-react-19';\r\nimport { CrownTwoTone } from '@ant-design/icons';\r\n\r\nconst isDev = process.env.NODE_ENV === 'development';\r\nconst loginPath = '/user/login';\r\nconst teamSelectPath = '/user/team-select';\r\nconst teamCreatePath = '/team/create';\r\n\r\n// 不需要认证的路径\r\nconst noAuthPaths = [\r\n  loginPath,\r\n  teamSelectPath,\r\n  teamCreatePath,\r\n  '/404',\r\n];\r\n\r\n/**\r\n * 全局初始状态\r\n */\r\nexport interface InitialState {\r\n  settings?: Partial<LayoutSettings>;\r\n  currentUser?: UserProfileResponse;\r\n  currentTeam?: TeamDetailResponse;\r\n  loading?: boolean;\r\n  fetchUserInfo?: () => Promise<UserProfileResponse | undefined>;\r\n  fetchTeamInfo?: () => Promise<TeamDetailResponse | undefined>;\r\n}\r\n\r\n/**\r\n * @see https://umijs.org/docs/api/runtime-config#getinitialstate\r\n * */\r\nexport async function getInitialState(): Promise<InitialState> {\r\n  const fetchUserInfo = async (): Promise<UserProfileResponse | undefined> => {\r\n    try {\r\n      if (!AuthService.isLoggedIn()) {\r\n        return undefined;\r\n      }\r\n\r\n      const userProfile = await UserService.getUserProfile();\r\n      return userProfile;\r\n    } catch (error) {\r\n      console.error('获取用户信息失败:', error);\r\n      // 只有在确实是认证错误时才清除 Token\r\n      if (error?.message?.includes('401') || error?.message?.includes('未认证')) {\r\n        AuthService.clearToken();\r\n      }\r\n      return undefined;\r\n    }\r\n  };\r\n\r\n  const fetchTeamInfo = async (): Promise<TeamDetailResponse | undefined> => {\r\n    try {\r\n      const teamDetail = await TeamService.getCurrentTeamDetail();\r\n      return teamDetail;\r\n    } catch (error) {\r\n      console.error('获取团队信息失败:', error);\r\n      return undefined;\r\n    }\r\n  };\r\n\r\n  const { location } = history;\r\n  const currentPath = location.pathname;\r\n\r\n  // 如果是不需要认证的页面，直接返回基础状态\r\n  if (noAuthPaths.some(path => currentPath.startsWith(path))) {\r\n    return {\r\n      fetchUserInfo,\r\n      fetchTeamInfo,\r\n      settings: defaultSettings as Partial<LayoutSettings>,\r\n    };\r\n  }\r\n\r\n  // 检查认证状态\r\n  if (!AuthService.isLoggedIn()) {\r\n    history.push(loginPath);\r\n    return {\r\n      fetchUserInfo,\r\n      fetchTeamInfo,\r\n      settings: defaultSettings as Partial<LayoutSettings>,\r\n    };\r\n  }\r\n\r\n  // 获取用户信息\r\n  const currentUser = await fetchUserInfo();\r\n  if (!currentUser) {\r\n    history.push(loginPath);\r\n    return {\r\n      fetchUserInfo,\r\n      fetchTeamInfo,\r\n      settings: defaultSettings as Partial<LayoutSettings>,\r\n    };\r\n  }\r\n\r\n  // 获取团队信息\r\n  const currentTeam = await fetchTeamInfo();\r\n\r\n  // 如果没有团队信息且不在团队相关页面，跳转到团队选择页面\r\n  if (!currentTeam && ![teamSelectPath, teamCreatePath].includes(currentPath)) {\r\n    history.push(teamSelectPath);\r\n    return {\r\n      fetchUserInfo,\r\n      fetchTeamInfo,\r\n      currentUser,\r\n      settings: defaultSettings as Partial<LayoutSettings>,\r\n    };\r\n  }\r\n\r\n  return {\r\n    fetchUserInfo,\r\n    fetchTeamInfo,\r\n    currentUser,\r\n    currentTeam,\r\n    settings: defaultSettings as Partial<LayoutSettings>,\r\n  };\r\n}\r\n\r\n// ProLayout 支持的api https://procomponents.ant.design/components/layout\r\nexport const layout: RunTimeLayoutConfig = ({\r\n  initialState,\r\n  setInitialState,\r\n}) => {\r\n  return {\r\n    actionsRender: () => [\r\n      <Question key=\"doc\" />,\r\n    ],\r\n    avatarProps: {\r\n      src: <CrownTwoTone />,\r\n      title: <AvatarName />,\r\n      render: (_, avatarChildren) => {\r\n        return <AvatarDropdown menu={true}>{avatarChildren}</AvatarDropdown>;\r\n      },\r\n    },\r\n    waterMarkProps: {\r\n      content: initialState?.currentUser?.name,\r\n    },\r\n    footerRender: () => <Footer />,\r\n    onPageChange: () => {\r\n      const { location } = history;\r\n      const currentPath = location.pathname;\r\n\r\n      // 如果是不需要认证的页面，不做处理\r\n      if (noAuthPaths.some(path => currentPath.startsWith(path))) {\r\n        return;\r\n      }\r\n\r\n      // 检查登录状态\r\n      if (!AuthService.isLoggedIn()) {\r\n        history.push(loginPath);\r\n        return;\r\n      }\r\n\r\n      // 检查团队选择状态 - 使用 initialState.currentTeam\r\n      if (!initialState?.currentTeam && ![teamSelectPath, teamCreatePath].includes(currentPath)) {\r\n        history.push(teamSelectPath);\r\n        return;\r\n      }\r\n    },\r\n    // 移除背景图片，使用简洁的设计\r\n    bgLayoutImgList: [],\r\n    links: [],\r\n    menuHeaderRender: undefined,\r\n    // 自定义 403 页面\r\n    // unAccessible: <div>unAccessible</div>,\r\n    // 增加一个 loading 的状态\r\n    childrenRender: (children) => {\r\n      return (\r\n        <ErrorBoundary>\r\n          <GlobalLoading>\r\n            {children}\r\n            {isDev && (\r\n              <SettingDrawer\r\n                disableUrlParams\r\n                enableDarkTheme\r\n                settings={initialState?.settings}\r\n                onSettingChange={(settings) => {\r\n                  setInitialState((preInitialState) => ({\r\n                    ...preInitialState,\r\n                    settings,\r\n                  }));\r\n                }}\r\n              />\r\n            )}\r\n          </GlobalLoading>\r\n        </ErrorBoundary>\r\n      );\r\n    },\r\n    // 显示当前团队信息\r\n    title: initialState?.currentTeam?.name || 'TeamAuth',\r\n    logo: '/logo.svg',\r\n    ...initialState?.settings,\r\n  };\r\n};\r\n\r\n/**\r\n * @name request 配置，可以配置错误处理\r\n * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。\r\n * @doc https://umijs.org/docs/max/request#配置\r\n */\r\nexport const request: RequestConfig = {\r\n  baseURL: '/api', // 使用代理，相对路径\r\n  ...errorConfig,\r\n};\r\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBC6CS,eAAe;2BAAf;;gBAsFT,MAAM;2BAAN;;gBAiFA,OAAO;2BAAP;;;;;;;kDArNiB;wCAEN;+CAQjB;6CAC+C;6FAE1B;uDACA;6BACrB;0CACsB;;;;;;;;;YAE7B,MAAM,QAAQ;YACd,MAAM,YAAY;YAClB,MAAM,iBAAiB;YACvB,MAAM,iBAAiB;YAEvB,WAAW;YACX,MAAM,cAAc;gBAClB;gBACA;gBACA;gBACA;aACD;YAiBM,eAAe;gBACpB,MAAM,gBAAgB;oBACpB,IAAI;wBACF,IAAI,CAAC,qBAAW,CAAC,UAAU,IACzB,OAAO;wBAGT,MAAM,cAAc,MAAM,qBAAW,CAAC,cAAc;wBACpD,OAAO;oBACT,EAAE,OAAO,OAAO;4BAGV,gBAAmC;wBAFvC,QAAQ,KAAK,CAAC,aAAa;wBAC3B,uBAAuB;wBACvB,IAAI,CAAA,kBAAA,6BAAA,iBAAA,MAAO,OAAO,cAAd,qCAAA,eAAgB,QAAQ,CAAC,YAAU,kBAAA,6BAAA,kBAAA,MAAO,OAAO,cAAd,sCAAA,gBAAgB,QAAQ,CAAC,SAC9D,qBAAW,CAAC,UAAU;wBAExB,OAAO;oBACT;gBACF;gBAEA,MAAM,gBAAgB;oBACpB,IAAI;wBACF,MAAM,aAAa,MAAM,qBAAW,CAAC,oBAAoB;wBACzD,OAAO;oBACT,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,OAAO;oBACT;gBACF;gBAEA,MAAM,EAAE,QAAQ,EAAE,GAAG,YAAO;gBAC5B,MAAM,cAAc,SAAS,QAAQ;gBAErC,uBAAuB;gBACvB,IAAI,YAAY,IAAI,CAAC,CAAA,OAAQ,YAAY,UAAU,CAAC,QAClD,OAAO;oBACL;oBACA;oBACA,UAAU,wBAAe;gBAC3B;gBAGF,SAAS;gBACT,IAAI,CAAC,qBAAW,CAAC,UAAU,IAAI;oBAC7B,YAAO,CAAC,IAAI,CAAC;oBACb,OAAO;wBACL;wBACA;wBACA,UAAU,wBAAe;oBAC3B;gBACF;gBAEA,SAAS;gBACT,MAAM,cAAc,MAAM;gBAC1B,IAAI,CAAC,aAAa;oBAChB,YAAO,CAAC,IAAI,CAAC;oBACb,OAAO;wBACL;wBACA;wBACA,UAAU,wBAAe;oBAC3B;gBACF;gBAEA,SAAS;gBACT,MAAM,cAAc,MAAM;gBAE1B,8BAA8B;gBAC9B,IAAI,CAAC,eAAe,CAAC;oBAAC;oBAAgB;iBAAe,CAAC,QAAQ,CAAC,cAAc;oBAC3E,YAAO,CAAC,IAAI,CAAC;oBACb,OAAO;wBACL;wBACA;wBACA;wBACA,UAAU,wBAAe;oBAC3B;gBACF;gBAEA,OAAO;oBACL;oBACA;oBACA;oBACA;oBACA,UAAU,wBAAe;gBAC3B;YACF;YAGO,MAAM,SAA8B,CAAC,EAC1C,YAAY,EACZ,eAAe,EAChB;oBAac,2BAsDJ;gBAlET,OAAO;oBACL,eAAe,IAAM;0CACnB,2BAAC,oBAAQ,MAAK;;;;;yBACf;oBACD,aAAa;wBACX,mBAAK,2BAAC,mBAAY;;;;;wBAClB,qBAAO,2BAAC,sBAAU;;;;;wBAClB,QAAQ,CAAC,GAAG;4BACV,qBAAO,2BAAC,0BAAc;gCAAC,MAAM;0CAAO;;;;;;wBACtC;oBACF;oBACA,gBAAgB;wBACd,OAAO,EAAE,yBAAA,oCAAA,4BAAA,aAAc,WAAW,cAAzB,gDAAA,0BAA2B,IAAI;oBAC1C;oBACA,cAAc,kBAAM,2BAAC,kBAAM;;;;;oBAC3B,cAAc;wBACZ,MAAM,EAAE,QAAQ,EAAE,GAAG,YAAO;wBAC5B,MAAM,cAAc,SAAS,QAAQ;wBAErC,mBAAmB;wBACnB,IAAI,YAAY,IAAI,CAAC,CAAA,OAAQ,YAAY,UAAU,CAAC,QAClD;wBAGF,SAAS;wBACT,IAAI,CAAC,qBAAW,CAAC,UAAU,IAAI;4BAC7B,YAAO,CAAC,IAAI,CAAC;4BACb;wBACF;wBAEA,yCAAyC;wBACzC,IAAI,EAAC,yBAAA,mCAAA,aAAc,WAAW,KAAI,CAAC;4BAAC;4BAAgB;yBAAe,CAAC,QAAQ,CAAC,cAAc;4BACzF,YAAO,CAAC,IAAI,CAAC;4BACb;wBACF;oBACF;oBACA,iBAAiB;oBACjB,iBAAiB,EAAE;oBACnB,OAAO,EAAE;oBACT,kBAAkB;oBAClB,aAAa;oBACb,yCAAyC;oBACzC,mBAAmB;oBACnB,gBAAgB,CAAC;wBACf,qBACE,2BAAC,yBAAa;sCACZ,cAAA,2BAAC,yBAAa;;oCACX;oCACA,uBACC,2BAAC,4BAAa;wCACZ,gBAAgB;wCAChB,eAAe;wCACf,QAAQ,EAAE,yBAAA,mCAAA,aAAc,QAAQ;wCAChC,iBAAiB,CAAC;4CAChB,gBAAgB,CAAC,kBAAqB,CAAA;oDACpC,GAAG,eAAe;oDAClB;gDACF,CAAA;wCACF;;;;;;;;;;;;;;;;;oBAMZ;oBACA,WAAW;oBACX,OAAO,CAAA,yBAAA,oCAAA,4BAAA,aAAc,WAAW,cAAzB,gDAAA,0BAA2B,IAAI,KAAI;oBAC1C,MAAM;uBACH,yBAAA,mCAAA,aAAc,QAAQ,AAAzB;gBACF;YACF;YAOO,MAAM,UAAyB;gBACpC,SAAS;gBACT,GAAG,+BAAW;YAChB;;;;;;;;;;;;;;;;;;;;;;;IDvNc;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;QAAC,wCAAuC;YAAC;SAAuC;IAAA;;AAC9yB"}