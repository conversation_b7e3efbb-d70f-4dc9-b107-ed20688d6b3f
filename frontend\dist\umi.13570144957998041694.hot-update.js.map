{"version": 3, "sources": ["umi.13570144957998041694.hot-update.js", "src/components/TeamSwitcher/index.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='5540011554859289253';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/create/index.tsx\":[\"p__team__create__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "/**\n * 团队切换组件\n * 显示当前团队信息并提供团队切换功能\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Button, Dropdown, Space, Typography, Avatar, message, Spin } from 'antd';\nimport { TeamOutlined, SwapOutlined, DownOutlined } from '@ant-design/icons';\nimport { history, useModel } from '@umijs/max';\nimport { createStyles } from 'antd-style';\nimport { TeamService, AuthService } from '@/services';\nimport type { TeamDetailResponse } from '@/types/api';\n\nconst { Text } = Typography;\n\nconst useStyles = createStyles(({ token }) => ({\n  teamSwitcher: {\n    display: 'flex',\n    alignItems: 'center',\n    padding: '4px 12px',\n    borderRadius: token.borderRadius,\n    backgroundColor: 'rgba(255, 255, 255, 0.1)',\n    border: '1px solid rgba(255, 255, 255, 0.2)',\n    cursor: 'pointer',\n    transition: 'all 0.3s',\n    '&:hover': {\n      backgroundColor: 'rgba(255, 255, 255, 0.15)',\n      borderColor: 'rgba(255, 255, 255, 0.3)',\n    },\n  },\n  teamInfo: {\n    display: 'flex',\n    alignItems: 'center',\n    gap: 8,\n  },\n  teamName: {\n    color: 'rgba(255, 255, 255, 0.85)',\n    fontWeight: 500,\n    maxWidth: 150,\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    whiteSpace: 'nowrap',\n  },\n  dropdownItem: {\n    display: 'flex',\n    alignItems: 'center',\n    gap: 8,\n    padding: '8px 12px',\n    minWidth: 200,\n  },\n  currentTeamItem: {\n    backgroundColor: token.colorPrimaryBg,\n  },\n}));\n\ninterface TeamSwitcherProps {\n  style?: React.CSSProperties;\n}\n\nconst TeamSwitcher: React.FC<TeamSwitcherProps> = ({ style }) => {\n  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [dropdownVisible, setDropdownVisible] = useState(false);\n  const { styles } = useStyles();\n  const { initialState, setInitialState } = useModel('@@initialState');\n\n  const currentTeam = initialState?.currentTeam;\n\n  // 获取用户的所有团队\n  const fetchTeams = async () => {\n    try {\n      setLoading(true);\n      const teamList = await TeamService.getUserTeams();\n      setTeams(teamList);\n    } catch (error) {\n      console.error('获取团队列表失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    if (dropdownVisible) {\n      fetchTeams();\n    }\n  }, [dropdownVisible]);\n\n  // 切换团队\n  const handleTeamSwitch = async (teamId: number) => {\n    if (teamId === currentTeam?.id) {\n      setDropdownVisible(false);\n      return;\n    }\n\n    try {\n      setLoading(true);\n      const response = await AuthService.selectTeam({ teamId });\n\n      // 检查后端返回的团队选择成功标识\n      if (response.teamSelectionSuccess && response.team && response.team.id === teamId) {\n        message.success('团队切换成功！');\n\n        // 刷新 initialState 以更新团队信息\n        if (initialState?.fetchTeamInfo && initialState?.fetchUserInfo && setInitialState) {\n          try {\n            const [currentUser, currentTeam] = await Promise.all([\n              initialState.fetchUserInfo(),\n              initialState.fetchTeamInfo()\n            ]);\n\n            // 确保团队信息已正确获取\n            if (currentTeam && currentTeam.id === teamId) {\n              await setInitialState({\n                ...initialState,\n                currentUser,\n                currentTeam,\n              });\n            } else {\n              console.error('获取的团队信息与选择的团队不匹配');\n              message.error('团队切换失败，请重试');\n            }\n          } catch (error) {\n            console.error('更新 initialState 失败:', error);\n            message.error('团队切换失败，请重试');\n          }\n        }\n\n        setDropdownVisible(false);\n      } else {\n        console.error('团队切换响应异常，未返回正确的团队信息');\n        message.error('团队切换失败，请重试');\n      }\n    } catch (error) {\n      console.error('团队切换失败:', error);\n      message.error('团队切换失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 跳转到团队管理页面\n  const handleManageTeams = () => {\n    setDropdownVisible(false);\n    history.push('/team/list');\n  };\n\n  // 跳转到团队选择页面\n  const handleSelectTeam = () => {\n    setDropdownVisible(false);\n    history.push('/user/team-select');\n  };\n\n  if (!currentTeam) {\n    return null;\n  }\n\n  const dropdownItems = [\n    // 当前团队\n    {\n      key: 'current',\n      label: (\n        <div className={`${styles.dropdownItem} ${styles.currentTeamItem}`}>\n          <Avatar size=\"small\" icon={<TeamOutlined />} />\n          <div>\n            <div style={{ fontWeight: 500 }}>{currentTeam.name}</div>\n            <Text type=\"secondary\" style={{ fontSize: 12 }}>当前团队</Text>\n          </div>\n        </div>\n      ),\n      disabled: true,\n    },\n    {\n      type: 'divider' as const,\n    },\n    // 其他团队\n    ...teams\n      .filter(team => team.id !== currentTeam.id)\n      .map(team => ({\n        key: team.id.toString(),\n        label: (\n          <div className={styles.dropdownItem}>\n            <Avatar size=\"small\" icon={<TeamOutlined />} />\n            <div>\n              <div>{team.name}</div>\n              <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                {team.memberCount} 名成员\n              </Text>\n            </div>\n          </div>\n        ),\n        onClick: () => handleTeamSwitch(team.id),\n      })),\n    {\n      type: 'divider' as const,\n    },\n    // 管理选项\n    {\n      key: 'select',\n      label: (\n        <div className={styles.dropdownItem}>\n          <SwapOutlined />\n          <span>选择团队</span>\n        </div>\n      ),\n      onClick: handleSelectTeam,\n    },\n    {\n      key: 'manage',\n      label: (\n        <div className={styles.dropdownItem}>\n          <TeamOutlined />\n          <span>管理团队</span>\n        </div>\n      ),\n      onClick: handleManageTeams,\n    },\n  ];\n\n  return (\n    <Dropdown\n      menu={{ items: dropdownItems }}\n      trigger={['click']}\n      open={dropdownVisible}\n      onOpenChange={setDropdownVisible}\n      placement=\"bottomRight\"\n    >\n      <div className={styles.teamSwitcher} style={style}>\n        <Spin spinning={loading} size=\"small\">\n          <div className={styles.teamInfo}>\n            <Avatar size=\"small\" icon={<TeamOutlined />} />\n            <Text className={styles.teamName}>{currentTeam.name}</Text>\n            <DownOutlined style={{ color: 'rgba(255, 255, 255, 0.65)', fontSize: 12 }} />\n          </div>\n        </Spin>\n      </div>\n    </Dropdown>\n  );\n};\n\nexport default TeamSwitcher;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;wCC4Ob;;;2BAAA;;;;;;oFA1O2C;yCACgC;0CAClB;wCACvB;8CACL;6CACY;;;;;;;;;;YAGzC,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;YAE3B,MAAM,YAAY,IAAA,uBAAY,EAAC,CAAC,EAAE,KAAK,EAAE,GAAM,CAAA;oBAC7C,cAAc;wBACZ,SAAS;wBACT,YAAY;wBACZ,SAAS;wBACT,cAAc,MAAM,YAAY;wBAChC,iBAAiB;wBACjB,QAAQ;wBACR,QAAQ;wBACR,YAAY;wBACZ,WAAW;4BACT,iBAAiB;4BACjB,aAAa;wBACf;oBACF;oBACA,UAAU;wBACR,SAAS;wBACT,YAAY;wBACZ,KAAK;oBACP;oBACA,UAAU;wBACR,OAAO;wBACP,YAAY;wBACZ,UAAU;wBACV,UAAU;wBACV,cAAc;wBACd,YAAY;oBACd;oBACA,cAAc;wBACZ,SAAS;wBACT,YAAY;wBACZ,KAAK;wBACL,SAAS;wBACT,UAAU;oBACZ;oBACA,iBAAiB;wBACf,iBAAiB,MAAM,cAAc;oBACvC;gBACF,CAAA;YAMA,MAAM,eAA4C,CAAC,EAAE,KAAK,EAAE;;gBAC1D,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAuB,EAAE;gBAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAC;gBACvD,MAAM,EAAE,MAAM,EAAE,GAAG;gBACnB,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;gBAEnD,MAAM,cAAc,yBAAA,mCAAA,aAAc,WAAW;gBAE7C,YAAY;gBACZ,MAAM,aAAa;oBACjB,IAAI;wBACF,WAAW;wBACX,MAAM,WAAW,MAAM,qBAAW,CAAC,YAAY;wBAC/C,SAAS;oBACX,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;oBAC7B,SAAU;wBACR,WAAW;oBACb;gBACF;gBAEA,IAAA,gBAAS,EAAC;oBACR,IAAI,iBACF;gBAEJ,GAAG;oBAAC;iBAAgB;gBAEpB,OAAO;gBACP,MAAM,mBAAmB,OAAO;oBAC9B,IAAI,YAAW,wBAAA,kCAAA,YAAa,EAAE,GAAE;wBAC9B,mBAAmB;wBACnB;oBACF;oBAEA,IAAI;wBACF,WAAW;wBACX,MAAM,WAAW,MAAM,qBAAW,CAAC,UAAU,CAAC;4BAAE;wBAAO;wBAEvD,kBAAkB;wBAClB,IAAI,SAAS,oBAAoB,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,EAAE,KAAK,QAAQ;4BACjF,aAAO,CAAC,OAAO,CAAC;4BAEhB,0BAA0B;4BAC1B,IAAI,CAAA,yBAAA,mCAAA,aAAc,aAAa,MAAI,yBAAA,mCAAA,aAAc,aAAa,KAAI,iBAChE,IAAI;gCACF,MAAM,CAAC,aAAa,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;oCACnD,aAAa,aAAa;oCAC1B,aAAa,aAAa;iCAC3B;gCAED,cAAc;gCACd,IAAI,eAAe,YAAY,EAAE,KAAK,QACpC,MAAM,gBAAgB;oCACpB,GAAG,YAAY;oCACf;oCACA;gCACF;qCACK;oCACL,QAAQ,KAAK,CAAC;oCACd,aAAO,CAAC,KAAK,CAAC;gCAChB;4BACF,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,uBAAuB;gCACrC,aAAO,CAAC,KAAK,CAAC;4BAChB;4BAGF,mBAAmB;wBACrB,OAAO;4BACL,QAAQ,KAAK,CAAC;4BACd,aAAO,CAAC,KAAK,CAAC;wBAChB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB,SAAU;wBACR,WAAW;oBACb;gBACF;gBAEA,YAAY;gBACZ,MAAM,oBAAoB;oBACxB,mBAAmB;oBACnB,YAAO,CAAC,IAAI,CAAC;gBACf;gBAEA,YAAY;gBACZ,MAAM,mBAAmB;oBACvB,mBAAmB;oBACnB,YAAO,CAAC,IAAI,CAAC;gBACf;gBAEA,IAAI,CAAC,aACH,OAAO;gBAGT,MAAM,gBAAgB;oBACpB,OAAO;oBACP;wBACE,KAAK;wBACL,qBACE,2BAAC;4BAAI,WAAW,CAAC,EAAE,OAAO,YAAY,CAAC,CAAC,EAAE,OAAO,eAAe,CAAC,CAAC;;8CAChE,2BAAC,YAAM;oCAAC,MAAK;oCAAQ,oBAAM,2BAAC,mBAAY;;;;;;;;;;8CACxC,2BAAC;;sDACC,2BAAC;4CAAI,OAAO;gDAAE,YAAY;4CAAI;sDAAI,YAAY,IAAI;;;;;;sDAClD,2BAAC;4CAAK,MAAK;4CAAY,OAAO;gDAAE,UAAU;4CAAG;sDAAG;;;;;;;;;;;;;;;;;;wBAItD,UAAU;oBACZ;oBACA;wBACE,MAAM;oBACR;oBACA,OAAO;uBACJ,MACA,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,YAAY,EAAE,EACzC,GAAG,CAAC,CAAA,OAAS,CAAA;4BACZ,KAAK,KAAK,EAAE,CAAC,QAAQ;4BACrB,qBACE,2BAAC;gCAAI,WAAW,OAAO,YAAY;;kDACjC,2BAAC,YAAM;wCAAC,MAAK;wCAAQ,oBAAM,2BAAC,mBAAY;;;;;;;;;;kDACxC,2BAAC;;0DACC,2BAAC;0DAAK,KAAK,IAAI;;;;;;0DACf,2BAAC;gDAAK,MAAK;gDAAY,OAAO;oDAAE,UAAU;gDAAG;;oDAC1C,KAAK,WAAW;oDAAC;;;;;;;;;;;;;;;;;;;4BAK1B,SAAS,IAAM,iBAAiB,KAAK,EAAE;wBACzC,CAAA;oBACF;wBACE,MAAM;oBACR;oBACA,OAAO;oBACP;wBACE,KAAK;wBACL,qBACE,2BAAC;4BAAI,WAAW,OAAO,YAAY;;8CACjC,2BAAC,mBAAY;;;;;8CACb,2BAAC;8CAAK;;;;;;;;;;;;wBAGV,SAAS;oBACX;oBACA;wBACE,KAAK;wBACL,qBACE,2BAAC;4BAAI,WAAW,OAAO,YAAY;;8CACjC,2BAAC,mBAAY;;;;;8CACb,2BAAC;8CAAK;;;;;;;;;;;;wBAGV,SAAS;oBACX;iBACD;gBAED,qBACE,2BAAC,cAAQ;oBACP,MAAM;wBAAE,OAAO;oBAAc;oBAC7B,SAAS;wBAAC;qBAAQ;oBAClB,MAAM;oBACN,cAAc;oBACd,WAAU;8BAEV,cAAA,2BAAC;wBAAI,WAAW,OAAO,YAAY;wBAAE,OAAO;kCAC1C,cAAA,2BAAC,UAAI;4BAAC,UAAU;4BAAS,MAAK;sCAC5B,cAAA,2BAAC;gCAAI,WAAW,OAAO,QAAQ;;kDAC7B,2BAAC,YAAM;wCAAC,MAAK;wCAAQ,oBAAM,2BAAC,mBAAY;;;;;;;;;;kDACxC,2BAAC;wCAAK,WAAW,OAAO,QAAQ;kDAAG,YAAY,IAAI;;;;;;kDACnD,2BAAC,mBAAY;wCAAC,OAAO;4CAAE,OAAO;4CAA6B,UAAU;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;YAMpF;eAlLM;;oBAIe;oBACuB,aAAQ;;;iBAL9C;gBAoLN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;ID5OD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;QAAC,wCAAuC;YAAC;SAAuC;IAAA;;AAC9yB"}