{"version": 3, "sources": ["umi.18142552405579826978.hot-update.js", "src/components/index.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='2681871544370510714';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/create/index.tsx\":[\"p__team__create__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "/**\r\n * 这个文件作为组件的目录\r\n * 目的是统一管理对外输出的组件，方便分类\r\n */\r\n\r\n/**\r\n * 布局组件\r\n */\r\nimport Footer from './Footer';\r\nimport { AvatarDropdown, AvatarName } from './RightContent/AvatarDropdown';\r\n\r\n/**\r\n * 功能组件\r\n */\r\nimport GlobalLoading from './GlobalLoading';\r\nimport ErrorBoundary from './ErrorBoundary';\r\nimport TeamSwitcher from './TeamSwitcher';\r\n\r\nexport {\r\n  // 布局组件\r\n  AvatarDropdown,\r\n  AvatarName,\r\n  Footer,\r\n  Question,\r\n\r\n  // 功能组件\r\n  GlobalLoading,\r\n  ErrorBoundary,\r\n  TeamSwitcher,\r\n};\r\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBCgBX,OAAO;gBACP,cAAc;2BAAd,8BAAc;;gBACd,UAAU;2BAAV,0BAAU;;gBAMV,aAAa;2BAAb,sBAAa;;gBALb,MAAM;2BAAN,eAAM;;gBAGN,OAAO;gBACP,aAAa;2BAAb,sBAAa;;gBAHb,QAAQ;2BAAR;;gBAKA,YAAY;2BAAZ,qBAAY;;;;;;oFApBK;mDACwB;2FAKjB;2FACA;0FACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IDbX;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;QAAC,wCAAuC;YAAC;SAAuC;IAAA;;AAC9yB"}